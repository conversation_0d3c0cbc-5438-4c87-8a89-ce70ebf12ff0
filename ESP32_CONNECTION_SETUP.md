# 🔌 ESP32 Connection Setup Guide

## 🚨 **IMPORTANT: You Need to Update 2 Things in Your ESP32 Code**

### **Step 1: Update WiFi Credentials**
In `esp32_gait_sensor_4emg.ino`, find these lines and update them:

```cpp
// WiFi credentials - UPDATE THESE WITH YOUR NETWORK
const char* ssid = "YOUR_WIFI_SSID";        // Change to your WiFi name
const char* password = "YOUR_WIFI_PASSWORD"; // Change to your WiFi password
```

**Example:**
```cpp
const char* ssid = "MyHomeWiFi";
const char* password = "mypassword123";
```

### **Step 2: Update Server IP Address**
In the same file, find this line and update it:

```cpp
// Python server configuration - UPDATE THIS WITH YOUR COMPUTER'S IP
const char* server_ip = "*************";  // Change to your computer's IP address
```

## 🔍 **How to Find Your Computer's IP Address:**

### **Windows:**
1. Press `Win + R`, type `cmd`, press Enter
2. Type: `ipconfig`
3. Look for "IPv4 Address" under your WiFi adapter
4. Example: `*************`

### **Mac:**
1. Open Terminal
2. Type: `ifconfig | grep inet`
3. Look for your WiFi IP (usually starts with 192.168)

### **Linux:**
1. Open Terminal
2. Type: `ip addr show` or `hostname -I`

## 📝 **Complete Example Configuration:**

```cpp
// WiFi credentials
const char* ssid = "MyHomeWiFi";
const char* password = "mypassword123";

// Python server configuration
const char* server_ip = "*************";  // Your computer's IP
const int server_port = 8767;              // Python server port (don't change)
```

## 🚀 **Connection Flow:**

1. **ESP32** connects to your WiFi network
2. **ESP32** connects to Python server running on your computer
3. **ESP32** sends EMG/IMU data to Python server
4. **Python server** processes data and classifies gait patterns

## ✅ **Testing the Connection:**

### **Step 1: Start Python Server**
```bash
python run_system.py
# Choose option 1 (4 EMG system)
```

### **Step 2: Upload ESP32 Code**
1. Update WiFi credentials and server IP
2. Upload `esp32_gait_sensor_4emg.ino` to ESP32
3. Open Serial Monitor (115200 baud)

### **Step 3: Check Connection**
You should see:
```
Connecting to WiFi: MyHomeWiFi
WiFi connected! IP: *************
🔌 Connecting to Python server: ws://*************:8767
🎉 Connected to Python server: /
ESP32_GAIT_SENSOR_4EMG_READY
```

And in Python server:
```
🔌 New connection from *************
🎉 ESP32 IDENTIFIED! Connected from *************
```

## 🔧 **Troubleshooting:**

### **WiFi Connection Issues:**
- Double-check SSID and password
- Make sure ESP32 is in range of WiFi
- Try restarting ESP32

### **Server Connection Issues:**
- Verify your computer's IP address
- Make sure Python server is running first
- Check if firewall is blocking port 8767
- Ensure both devices are on same WiFi network

### **No Data Transmission:**
- Check Serial Monitor for error messages
- Verify EMG sensors are connected properly
- Make sure LO+/LO- pins are tied to VCC

## 🎯 **Success Indicators:**

✅ ESP32 connects to WiFi and shows IP address
✅ ESP32 connects to Python server
✅ Python server identifies ESP32
✅ Real-time EMG/IMU data appears in web interface
✅ Gait classification results are displayed

Once you see all these, your system is working perfectly! 🚀
