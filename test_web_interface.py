#!/usr/bin/env python3
"""
🧪 WEB INTERFACE TEST SCRIPT
Test the web interface connection to the Python server
"""

import asyncio
import websockets
import json
import time

async def test_web_interface():
    """Test web interface connection."""
    print("🧪 Testing Web Interface Connection")
    print("="*40)
    
    try:
        # Connect to the server
        uri = "ws://localhost:8767"
        print(f"🔌 Connecting to {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to server!")
            
            # Identify as web interface
            identification = {
                "type": "web_interface",
                "message": "Web interface test connection",
                "timestamp": time.time()
            }
            await websocket.send(json.dumps(identification))
            print("📤 Sent identification message")
            
            # Wait for welcome message
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                print(f"📨 Received: {data}")
                
                if data.get('type') == 'welcome':
                    print("🎉 Web interface successfully identified!")
                    print(f"   Message: {data.get('message')}")
                    print(f"   Model loaded: {data.get('model_loaded')}")
                    print(f"   ESP32 connected: {data.get('esp32_connected')}")
                else:
                    print(f"⚠️ Unexpected response: {data}")
                    
            except asyncio.TimeoutError:
                print("⏰ No response received within 5 seconds")
            
            # Keep connection open for a bit to test broadcasting
            print("\n🔄 Waiting for classification broadcasts...")
            print("   (Start ESP32 or demo mode to see results)")
            
            try:
                for i in range(10):  # Wait for up to 10 messages
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(message)
                    
                    if data.get('type') == 'classification':
                        print(f"🎯 Classification received:")
                        print(f"   Prediction: {data.get('prediction')}")
                        print(f"   Confidence: {data.get('confidence')}")
                        print(f"   Timestamp: {data.get('timestamp')}")
                    else:
                        print(f"📨 Other message: {data.get('type', 'unknown')}")
                        
            except asyncio.TimeoutError:
                print("⏰ No more messages received")
            
            print("\n✅ Web interface test completed!")
            
    except ConnectionRefusedError:
        print("❌ Connection refused - make sure the Python server is running!")
        print("   Run: python esp32_realtime_classifier_4emg.py")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_web_interface())
