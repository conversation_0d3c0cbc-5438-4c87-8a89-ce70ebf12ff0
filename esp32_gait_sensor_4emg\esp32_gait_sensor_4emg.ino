/*
🔬 ESP32 GAIT SENSOR SYSTEM - 4 EMG VERSION
4 AD8232 EMG sensors + 4 MPU6050 IMU sensors with I2C multiplexers
- 74HC4051 I2C multiplexer for EMG sensors
- TCA9548A I2C multiplexer for MPU6050 sensors
- Each AD8232 can connect to 4 EMG patches (total 16 patches possible)
*/

#include <WiFi.h>
#include <WebSocketsClient.h>
#include <Wire.h>
#include <MPU6050.h>
#include <ArduinoJson.h>

// WiFi credentials - UPDATE THESE WITH YOUR NETWORK
const char* ssid = "Munnuwifi";        // Change to your WiFi name
const char* password = "nikhil@12345"; // Change to your WiFi password

// WebSocket CLIENT to connect to Python server
WebSocketsClient webSocket;

// Python server configuration - UPDATE THIS WITH YOUR COMPUTER'S IP
const char* server_ip = "***********";  // Your computer's actual IP address
const int server_port = 8767;              // Python server port

// EMG sensor configuration (4 AD8232 sensors)
// OPTIMIZED: LO+/LO- connected directly to VCC (no lead-off detection)
const int NUM_EMG_SENSORS = 4;
const int EMG_ANALOG_PINS[NUM_EMG_SENSORS] = {36, 39, 34, 35}; // ADC1 pins only

// Lead-off detection DISABLED (LO+/LO- tied to VCC)
// This saves 8 GPIO pins for other uses!

// 74HC4051 Multiplexer NOT USED for AD8232
// (Only TCA9548A used for IMU sensors)

// TCA9548A I2C Multiplexer for MPU6050 sensors
const int TCA9548A_ADDRESS = 0x70;
const int NUM_IMU_SENSORS = 4;

// MPU6050 IMU sensors
MPU6050 imu_sensors[NUM_IMU_SENSORS];

// Sampling parameters
const int SAMPLE_RATE = 1000;  // 1000 Hz sampling
const int WINDOW_SIZE = 50;    // 50ms window (50 samples)

// Data buffers
int emg_buffer[NUM_EMG_SENSORS][WINDOW_SIZE];
int16_t acc_buffer[NUM_IMU_SENSORS][3][WINDOW_SIZE];  // 4 IMUs, 3 axes each
int16_t gyro_buffer[NUM_IMU_SENSORS][3][WINDOW_SIZE]; // 4 IMUs, 3 axes each

int sample_count = 0;
unsigned long last_sample_time = 0;
bool wifi_connected = false;

// Sensor location mapping
const char* emg_locations[NUM_EMG_SENSORS] = {
  "L_Thigh",    // EMG sensor 1 - Left Thigh (can connect 4 patches)
  "R_Thigh",    // EMG sensor 2 - Right Thigh (can connect 4 patches)
  "L_Calf",     // EMG sensor 3 - Left Calf (can connect 4 patches)
  "R_Calf"      // EMG sensor 4 - Right Calf (can connect 4 patches)
};

const char* imu_locations[NUM_IMU_SENSORS] = {
  "L_Thigh", "R_Thigh", "L_Calf", "R_Calf"
};

void setup() {
  Serial.begin(115200);
  Serial.println("ESP32 Gait Sensor - 4 EMG + 4 IMU with I2C Multiplexers");
  
  // Initialize EMG pins
  initializeEMGSensors();
  
  // Initialize I2C multiplexers
  initializeMultiplexers();
  
  // Initialize IMU sensors
  initializeIMUSensors();
  
  // Initialize WiFi
  initializeWiFi();
  
  // Initialize WebSocket CLIENT to connect to Python server
  if (wifi_connected) {
    webSocket.begin(server_ip, server_port, "/");
    webSocket.onEvent(webSocketEvent);
    Serial.printf("🔌 Connecting to Python server: ws://%s:%d\n", server_ip, server_port);
  }
  
  Serial.println("ESP32_GAIT_SENSOR_4EMG_READY");
  delay(1000);
}

void loop() {
  webSocket.loop();

  // Check WebSocket connection status
  static unsigned long last_connection_check = 0;
  if (millis() - last_connection_check > 5000) { // Check every 5 seconds
    if (wifi_connected && !webSocket.isConnected()) {
      Serial.println("🔄 Reconnecting to Python server...");
      webSocket.begin(server_ip, server_port, "/");
    }
    last_connection_check = millis();
  }

  unsigned long current_time = micros();
  
  // Sample at 1000 Hz (1000 microseconds interval)
  if (current_time - last_sample_time >= 1000) {
    
    // Read EMG data from all 4 sensors
    readEMGSensors();
    
    // Read IMU data from all 4 sensors
    readIMUSensors();
    
    sample_count++;
    last_sample_time = current_time;
    
    // When window is full, send data and reset
    if (sample_count >= WINDOW_SIZE) {
      sendDataWindow();
      sample_count = 0;
    }
  }
}

void initializeEMGSensors() {
  Serial.println("Initializing 4 EMG sensors (AD8232) - OPTIMIZED...");

  // Initialize only analog input pins (LO+/LO- connected to VCC)
  for (int i = 0; i < NUM_EMG_SENSORS; i++) {
    pinMode(EMG_ANALOG_PINS[i], INPUT);
  }

  Serial.println("✅ EMG sensors initialized (lead-off detection disabled)");
  Serial.println("💡 LO+/LO- pins connected directly to VCC");
}

void initializeMultiplexers() {
  Serial.println("Initializing I2C multiplexer (TCA9548A only)...");

  // Initialize I2C for IMU sensors only
  Wire.begin();
  Wire.setClock(400000); // 400kHz I2C speed

  // Test TCA9548A multiplexer (for IMU sensors)
  Wire.beginTransmission(TCA9548A_ADDRESS);
  if (Wire.endTransmission() == 0) {
    Serial.println("✅ TCA9548A I2C multiplexer found (for IMU sensors)");
  } else {
    Serial.println("❌ WARNING: TCA9548A not found!");
  }

  Serial.println("💡 74HC4051 not used (AD8232 connected directly)");
}

void initializeIMUSensors() {
  Serial.println("Initializing 4 IMU sensors (MPU6050)...");
  
  for (int i = 0; i < NUM_IMU_SENSORS; i++) {
    // Select I2C channel on TCA9548A
    selectTCA9548AChannel(i);
    
    // Initialize MPU6050
    imu_sensors[i].initialize();
    
    if (imu_sensors[i].testConnection()) {
      Serial.printf("IMU %d (%s) connected successfully\n", i, imu_locations[i]);
    } else {
      Serial.printf("ERROR: IMU %d (%s) connection failed\n", i, imu_locations[i]);
    }
    delay(100);
  }
}

void selectTCA9548AChannel(uint8_t channel) {
  if (channel > 7) return;
  
  Wire.beginTransmission(TCA9548A_ADDRESS);
  Wire.write(1 << channel);
  Wire.endTransmission();
}

// 74HC4051 multiplexer function REMOVED
// (Not needed - AD8232 sensors connected directly to ESP32)

void readEMGSensors() {
  // OPTIMIZED: Direct EMG reading (no lead-off detection)
  // LO+/LO- pins are tied to VCC, so we always read valid signals
  for (int i = 0; i < NUM_EMG_SENSORS; i++) {
    emg_buffer[i][sample_count] = analogRead(EMG_ANALOG_PINS[i]);
  }
}

void readIMUSensors() {
  for (int i = 0; i < NUM_IMU_SENSORS; i++) {
    // Select I2C channel for this IMU
    selectTCA9548AChannel(i);
    
    int16_t ax, ay, az, gx, gy, gz;
    
    // Read motion data
    imu_sensors[i].getMotion6(&ax, &ay, &az, &gx, &gy, &gz);
    
    // Store in buffers
    acc_buffer[i][0][sample_count] = ax;
    acc_buffer[i][1][sample_count] = ay;
    acc_buffer[i][2][sample_count] = az;
    gyro_buffer[i][0][sample_count] = gx;
    gyro_buffer[i][1][sample_count] = gy;
    gyro_buffer[i][2][sample_count] = gz;
  }
}

void initializeWiFi() {
  Serial.printf("Connecting to WiFi: %s\n", ssid);
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    wifi_connected = true;
    Serial.printf("\nWiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("🔌 Will connect to Python server: ws://%s:%d\n", server_ip, server_port);
  } else {
    wifi_connected = false;
    Serial.println("\nWiFi connection failed - using Serial only");
  }
}

void sendDataWindow() {
  // Create JSON data packet
  DynamicJsonDocument doc(8192);
  
  doc["timestamp"] = millis();
  doc["window_size"] = WINDOW_SIZE;
  doc["sample_rate"] = SAMPLE_RATE;
  doc["sensor_config"] = "4_EMG_4_IMU";
  
  // Add EMG data
  JsonArray emg_data = doc.createNestedArray("emg");
  for (int sensor = 0; sensor < NUM_EMG_SENSORS; sensor++) {
    JsonObject sensor_obj = emg_data.createNestedObject();
    sensor_obj["location"] = emg_locations[sensor];
    sensor_obj["sensor_id"] = sensor;
    sensor_obj["patches_possible"] = 4; // Each AD8232 can handle 4 patches
    
    JsonArray samples = sensor_obj.createNestedArray("samples");
    for (int i = 0; i < WINDOW_SIZE; i++) {
      samples.add(emg_buffer[sensor][i]);
    }
  }
  
  // Add IMU data
  JsonArray imu_data = doc.createNestedArray("imu");
  for (int sensor = 0; sensor < NUM_IMU_SENSORS; sensor++) {
    JsonObject imu_obj = imu_data.createNestedObject();
    imu_obj["location"] = imu_locations[sensor];
    imu_obj["sensor_id"] = sensor;
    
    // Accelerometer data
    JsonObject acc_obj = imu_obj.createNestedObject("accelerometer");
    JsonArray acc_x = acc_obj.createNestedArray("x");
    JsonArray acc_y = acc_obj.createNestedArray("y");
    JsonArray acc_z = acc_obj.createNestedArray("z");
    
    for (int i = 0; i < WINDOW_SIZE; i++) {
      acc_x.add(acc_buffer[sensor][0][i]);
      acc_y.add(acc_buffer[sensor][1][i]);
      acc_z.add(acc_buffer[sensor][2][i]);
    }
    
    // Gyroscope data
    JsonObject gyro_obj = imu_obj.createNestedObject("gyroscope");
    JsonArray gyro_x = gyro_obj.createNestedArray("x");
    JsonArray gyro_y = gyro_obj.createNestedArray("y");
    JsonArray gyro_z = gyro_obj.createNestedArray("z");
    
    for (int i = 0; i < WINDOW_SIZE; i++) {
      gyro_x.add(gyro_buffer[sensor][0][i]);
      gyro_y.add(gyro_buffer[sensor][1][i]);
      gyro_z.add(gyro_buffer[sensor][2][i]);
    }
  }
  
  // Send via WebSocket (WiFi) to Python server
  if (wifi_connected && webSocket.isConnected()) {
    String json_string;
    serializeJson(doc, json_string);

    // Check data size
    if (json_string.length() > 50000) {
      Serial.printf("⚠ Large data packet: %d bytes\n", json_string.length());
    }

    webSocket.sendTXT(json_string);
    delay(10); // Small delay to prevent overwhelming the server
  }
  
  // Also send via Serial for debugging
  Serial.println("DATA_START_4EMG");
  serializeJson(doc, Serial);
  Serial.println("\nDATA_END_4EMG");
}

void webSocketEvent(WStype_t type, uint8_t * payload, size_t length) {
  switch(type) {
    case WStype_DISCONNECTED:
      Serial.println("🔌 Disconnected from Python server");
      Serial.println("🔄 Will attempt reconnection in 5 seconds...");
      break;

    case WStype_CONNECTED:
      {
        Serial.printf("🎉 Connected to Python server: %s\n", payload);

        // Send identification message
        DynamicJsonDocument welcome(512);
        welcome["message"] = "ESP32 Gait Sensor Connected - 4 EMG Version";
        welcome["emg_sensors"] = NUM_EMG_SENSORS;
        welcome["imu_sensors"] = NUM_IMU_SENSORS;
        welcome["sample_rate"] = SAMPLE_RATE;
        welcome["window_size"] = WINDOW_SIZE;
        welcome["patches_per_emg"] = 4;
        welcome["total_patches_possible"] = NUM_EMG_SENSORS * 4;

        String welcome_string;
        serializeJson(welcome, welcome_string);
        webSocket.sendTXT(welcome_string);
      }
      break;

    case WStype_TEXT:
      Serial.printf("📨 Received from server: %s\n", payload);
      break;

    default:
      break;
  }
}