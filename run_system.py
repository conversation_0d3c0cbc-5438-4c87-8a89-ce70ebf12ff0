#!/usr/bin/env python3
"""
🚀 GAIT CLASSIFICATION SYSTEM LAUNCHER
Quick start script for the complete system
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_requirements():
    """Check if required files exist."""
    required_files = [
        "production_gait_classifier.pkl",
        "esp32_realtime_classifier_4emg.py",  # 4 EMG version (recommended)
        "esp32_realtime_classifier.py",       # 8 EMG version
        "web_interface.html",
        "data_simulator.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    return True

def check_model():
    """Check if the ML model is available."""
    model_path = "production_gait_classifier.pkl"
    if os.path.exists(model_path):
        size_mb = os.path.getsize(model_path) / (1024 * 1024)
        print(f"✅ ML Model found: {size_mb:.1f}MB")
        return True
    else:
        print("❌ ML Model not found!")
        print("   Run: python production_gait_classifier.py")
        return False

def start_classifier_server_with_connection_detection(emg_config="4emg"):
    """Start the real-time classifier server with ESP32 connection detection."""
    if emg_config == "4emg":
        script = "esp32_realtime_classifier_4emg.py"
        print("🚀 Starting 4 EMG classifier server...")
        port = 8767
    else:
        script = "esp32_realtime_classifier.py"
        print("🚀 Starting 8 EMG classifier server...")
        port = 8765

    try:
        # Start server without capturing output so it can run freely
        process = subprocess.Popen([
            sys.executable, "-u", script
        ])

        # Wait a moment for server to start
        time.sleep(3)

        # Check if server started successfully
        if process.poll() is not None:
            print(f"❌ Server failed to start (exit code: {process.returncode})")
            return None

        print(f"✅ Server started successfully on port {port}")
        print(f"🔍 Looking for ESP32 connection...")

        # Wait for ESP32 connection with timeout
        connection_detected = wait_for_esp32_connection(port, timeout=15)

        if connection_detected:
            print("🎉 ESP32 connected successfully!")
        else:
            print("⏰ No ESP32 connection detected after 15 seconds")
            choice = handle_no_esp32_connection(port)

            if choice == "demo":
                # Start data simulator with matching EMG configuration
                emg_count = "4" if emg_config == "4emg" else "8"
                print("🎮 Starting demo mode with data simulator...")
                sim_process = start_data_simulator(emg_count)
                if sim_process:
                    print("✅ Demo mode activated!")
                    return process, sim_process
                else:
                    print("❌ Failed to start demo mode")
                    print("🛑 Stopping server...")
                    process.terminate()
                    return None
            elif choice == "connected":
                print("🎉 ESP32 connection established!")
            elif choice == "continue":
                print("🚀 Continuing without ESP32 (server only mode)")
            elif choice == "stop":
                print("🛑 Stopping server as requested...")
                process.terminate()
                return None
            # If choice is anything else, just proceed

        return process, None

    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def wait_for_esp32_connection(port, timeout=15):
    """Wait for ESP32 connection with simple timeout - no false detection."""
    import time

    # Simple wait with progress indicator
    print(f"⏳ Waiting for ESP32 connection (timeout: {timeout}s)...")
    print("   📡 Make sure your ESP32 is powered on and connected to WiFi")
    print(f"   🔌 ESP32 should connect to: ws://YOUR_COMPUTER_IP:{port}")
    print("   💡 Server will detect ESP32 when it sends sensor data")
    print("   ⚠️  If ESP32 connects, you'll see 'ESP32 IDENTIFIED!' in server output")

    for i in range(timeout):
        print(f"⏳ Waiting for ESP32... {timeout-i}s remaining", end="\r")
        time.sleep(1)

    print()  # New line after countdown

    # Always return False to let user choose what to do
    # The server will properly detect ESP32 when it actually connects
    return False

def handle_no_esp32_connection(port=None):
    """Handle the case when no ESP32 connection is detected."""
    print("\n🤔 No ESP32 detected. What would you like to do?")
    print("1. 🎮 Start demo mode (with data simulator)")
    print("2. ⏳ Wait another 15 seconds for ESP32")
    print("3. 🚀 Continue without ESP32 (server only)")
    print("4. 🛑 Stop the server")

    while True:
        try:
            choice = input("\nEnter choice (1-4): ").strip()

            if choice == "1":
                return "demo"
            elif choice == "2":
                # Wait another 15 seconds
                print("\n⏳ Waiting another 15 seconds for ESP32...")
                connection_detected = wait_for_esp32_connection(port or 8767, timeout=15)

                if connection_detected:
                    print("🎉 ESP32 connected successfully!")
                    return "connected"
                else:
                    print("⏰ Still no ESP32 connection detected")
                    # Recursively ask again
                    return handle_no_esp32_connection(port)
            elif choice == "3":
                return "continue"
            elif choice == "4":
                return "stop"
            else:
                print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")
        except (EOFError, KeyboardInterrupt):
            print("\n🛑 Operation cancelled")
            return "stop"

def start_classifier_server_simple(emg_config="4emg"):
    """Start the classifier server without connection detection."""
    if emg_config == "4emg":
        script = "esp32_realtime_classifier_4emg.py"
        print("🚀 Starting 4 EMG classifier server...")
    else:
        script = "esp32_realtime_classifier.py"
        print("🚀 Starting 8 EMG classifier server...")

    try:
        process = subprocess.Popen([
            sys.executable, "-u", script
        ])
        time.sleep(3)  # Wait for server to start

        if process.poll() is not None:
            print(f"❌ Server failed to start (exit code: {process.returncode})")
            return None

        print("✅ Server started successfully")
        return process
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def start_data_simulator(emg_config="4"):
    """Start the data simulator for testing."""
    print(f"🎮 Starting data simulator with {emg_config} EMG configuration...")
    try:
        # Start simulator with EMG configuration argument
        process = subprocess.Popen([
            sys.executable, "-u", "data_simulator.py", emg_config
        ])
        return process
    except Exception as e:
        print(f"❌ Failed to start simulator: {e}")
        return None

def open_web_interface():
    """Open the web interface in browser."""
    web_path = Path("web_interface.html").absolute()
    web_url = f"file:///{web_path}"
    
    print("🌐 Opening web interface...")
    try:
        webbrowser.open(web_url)
        print(f"   URL: {web_url}")
    except Exception as e:
        print(f"❌ Failed to open browser: {e}")
        print(f"   Manually open: {web_path}")

def main():
    """Main launcher function."""
    print("🏥 GAIT CLASSIFICATION SYSTEM LAUNCHER")
    print("="*50)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ System check failed!")
        return
    
    if not check_model():
        print("\n❌ Model check failed!")
        return
    
    print("\n✅ All requirements satisfied!")
    
    # Ask user what to start
    print("\n🚀 What would you like to do?")
    print("1. 🔥 Start 4 EMG system (RECOMMENDED) - Auto-detect ESP32")
    print("2. ⚡ Start 8 EMG system (Advanced) - Auto-detect ESP32")
    print("3. 🎮 Start 4 EMG with demo mode (no ESP32 needed)")
    print("4. 🎮 Start 8 EMG with demo mode (no ESP32 needed)")
    print("5. 🌐 Open web interface only")
    print("6. 🤖 Train new model")

    choice = input("\nEnter choice (1-6): ").strip()
    
    if choice == "1":
        # Start 4 EMG system with ESP32 auto-detection
        print("\n🚀 Starting 4 EMG system with ESP32 auto-detection...")

        # Start server with connection detection
        result = start_classifier_server_with_connection_detection("4emg")
        if not result:
            print("❌ Failed to start 4 EMG system")
            return

        # Unpack result (could be just server_process or (server_process, sim_process))
        if isinstance(result, tuple):
            server_process, sim_process = result
        else:
            server_process, sim_process = result, None

        # Additional check in case server_process is None
        if not server_process:
            print("❌ Server process is not running")
            return

        # Give server time to start, then open web interface
        print("🌐 Opening web interface...")
        time.sleep(2)  # Give server time to start
        open_web_interface()

        print("\n✅ 4 EMG System started!")
        print("📊 4 EMG Server running")
        if sim_process:
            print("🎮 Demo mode active (data simulator running)")
        else:
            print("🔌 Ready for ESP32 connection")
        print("🌐 Web interface opened in browser")
        print("\nPress Ctrl+C to stop...")

        try:
            # Check if processes are still running
            if server_process.poll() is None:
                print("🔄 System running... waiting for Ctrl+C")
                server_process.wait()
            else:
                print("❌ Server process terminated unexpectedly")
                print(f"   Return code: {server_process.returncode}")
        except KeyboardInterrupt:
            print("\n🛑 Stopping system...")
            if server_process.poll() is None:
                server_process.terminate()
                server_process.wait(timeout=5)
            if sim_process and sim_process.poll() is None:
                sim_process.terminate()
                sim_process.wait(timeout=5)

    elif choice == "2":
        # Start 8 EMG system with ESP32 auto-detection
        print("\n🚀 Starting 8 EMG system with ESP32 auto-detection...")

        # Start server with connection detection
        result = start_classifier_server_with_connection_detection("8emg")
        if not result:
            print("❌ Failed to start 8 EMG system")
            return

        # Unpack result (could be just server_process or (server_process, sim_process))
        if isinstance(result, tuple):
            server_process, sim_process = result
        else:
            server_process, sim_process = result, None

        # Additional check in case server_process is None
        if not server_process:
            print("❌ Server process is not running")
            return

        # Open web interface
        open_web_interface()

        print("\n✅ 8 EMG System started!")
        print("📊 8 EMG Server running")
        if sim_process:
            print("🎮 Demo mode active (data simulator running)")
        else:
            print("🔌 Ready for ESP32 connection")
        print("🌐 Web interface opened in browser")
        print("\nPress Ctrl+C to stop...")

        try:
            # Check if processes are still running
            if server_process.poll() is None:
                print("🔄 System running... waiting for Ctrl+C")
                server_process.wait()
            else:
                print("❌ Server process terminated unexpectedly")
                print(f"   Return code: {server_process.returncode}")
        except KeyboardInterrupt:
            print("\n🛑 Stopping system...")
            if server_process.poll() is None:
                server_process.terminate()
                server_process.wait(timeout=5)
            if sim_process and sim_process.poll() is None:
                sim_process.terminate()
                sim_process.wait(timeout=5)

    elif choice == "3":
        # Start 4 EMG demo mode (no ESP32 needed)
        print("\n🎮 Starting 4 EMG demo mode (no ESP32 needed)...")

        # Start server
        server_process = start_classifier_server_simple("4emg")
        if not server_process:
            return

        # Start simulator with 4 EMG configuration
        sim_process = start_data_simulator("4")
        if not sim_process:
            server_process.terminate()
            return

        time.sleep(2)

        # Open web interface
        open_web_interface()

        print("\n✅ 4 EMG Demo system started!")
        print("📊 4 EMG Server running")
        print("🎮 Data simulator running (simulating ESP32)")
        print("🌐 Web interface opened")
        print("💡 This is demo mode - no real ESP32 needed!")
        print("\nPress Ctrl+C to stop...")

        try:
            # Check if both processes are still running
            if server_process.poll() is None and sim_process.poll() is None:
                print("🔄 Demo system running... waiting for Ctrl+C")
                server_process.wait()
            else:
                print("❌ One or more processes terminated unexpectedly")
        except KeyboardInterrupt:
            print("\n🛑 Stopping demo system...")
            if server_process.poll() is None:
                server_process.terminate()
                server_process.wait(timeout=5)
            if sim_process.poll() is None:
                sim_process.terminate()
                sim_process.wait(timeout=5)

    elif choice == "4":
        # Start 8 EMG demo mode (no ESP32 needed)
        print("\n🎮 Starting 8 EMG demo mode (no ESP32 needed)...")

        # Start server
        server_process = start_classifier_server_simple("8emg")
        if not server_process:
            return

        # Start simulator with 8 EMG configuration
        sim_process = start_data_simulator("8")
        if not sim_process:
            server_process.terminate()
            return

        time.sleep(2)

        # Open web interface
        open_web_interface()

        print("\n✅ 8 EMG Demo system started!")
        print("📊 8 EMG Server running")
        print("🎮 Data simulator running (simulating ESP32)")
        print("🌐 Web interface opened")
        print("💡 This is demo mode - no real ESP32 needed!")
        print("\nPress Ctrl+C to stop...")

        try:
            # Check if both processes are still running
            if server_process.poll() is None and sim_process.poll() is None:
                print("🔄 Demo system running... waiting for Ctrl+C")
                server_process.wait()
            else:
                print("❌ One or more processes terminated unexpectedly")
        except KeyboardInterrupt:
            print("\n🛑 Stopping demo system...")
            if server_process.poll() is None:
                server_process.terminate()
                server_process.wait(timeout=5)
            if sim_process.poll() is None:
                sim_process.terminate()
                sim_process.wait(timeout=5)

    elif choice == "5":
        # Open web interface only
        open_web_interface()
        print("✅ Web interface opened!")
        print("⚠️ Make sure server is running separately")

    elif choice == "6":
        # Train new model
        print("\n🤖 Training new model...")
        print("⚠️ This will take several minutes...")
        try:
            subprocess.run([sys.executable, "production_gait_classifier.py"])
        except KeyboardInterrupt:
            print("\n🛑 Training stopped by user")
    
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main()
