#!/usr/bin/env python3
"""
🔧 CONNECTION TEST SCRIPT
Simple WebSocket server to test ESP32 connection without ML processing
"""

import asyncio
import websockets
import json
import socket

async def handle_client(websocket, path):
    """Handle incoming WebSocket connections."""
    client_ip = websocket.remote_address[0] if websocket.remote_address else "unknown"
    print(f"🔌 New connection from {client_ip}")
    
    try:
        async for message in websocket:
            try:
                # Check message size
                message_size = len(message)
                print(f"📨 Received {message_size} bytes from {client_ip}")
                
                # Try to parse JSON
                data = json.loads(message)
                
                # Check if it's ESP32 data
                if 'emg' in data and 'imu' in data:
                    print(f"✅ Valid ESP32 data received from {client_ip}")
                    
                    # Send simple acknowledgment
                    response = {
                        "status": "received",
                        "timestamp": data.get("timestamp", 0),
                        "message": "Data received successfully"
                    }
                    await websocket.send(json.dumps(response))
                    
                elif 'message' in data:
                    print(f"💬 Message from {client_ip}: {data['message']}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error from {client_ip}: {e}")
            except Exception as e:
                print(f"❌ Error processing message from {client_ip}: {e}")
                
    except websockets.exceptions.ConnectionClosed as e:
        print(f"🔌 Connection closed from {client_ip} - Reason: {e}")
    except Exception as e:
        print(f"❌ Connection error from {client_ip}: {e}")

async def main():
    """Start the test server."""
    # Get local IP address
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    
    print("🧪 ESP32 CONNECTION TEST SERVER")
    print("="*40)
    print(f"🖥️  Computer IP: {local_ip}")
    print(f"📡 ESP32 should connect to: ws://{local_ip}:8767")
    print(f"🔧 This is a simple test server without ML processing")
    print()
    
    # Start server
    server = await websockets.serve(
        handle_client,
        "0.0.0.0",  # Listen on all interfaces
        8767        # Same port as main system
    )
    
    print(f"✅ Test server started on port 8767")
    print(f"🔍 Waiting for ESP32 connection...")
    print(f"💡 Press Ctrl+C to stop")
    print()
    
    try:
        await server.wait_closed()
    except KeyboardInterrupt:
        print("\n🛑 Test server stopped")

if __name__ == "__main__":
    asyncio.run(main())
