#!/usr/bin/env python3
"""
🎮 ESP32 DATA SIMULATOR
Simulates realistic EMG/IMU data for testing the gait classifier
"""

import asyncio
import websockets
import json
import numpy as np
import time
import random
from datetime import datetime

print("🎮 ESP32 DATA SIMULATOR")
print("🔬 Generating realistic EMG/IMU data for gait classification")
print("="*60)

class ESP32DataSimulator:
    def __init__(self, emg_sensors=4):
        self.is_running = False
        self.current_disease = "Normal_Gait"
        self.sample_rate = 1000  # 1000 Hz
        self.window_size = 50    # 50ms windows
        self.emg_sensors = emg_sensors  # 4 or 8 EMG sensors
        
        # Disease patterns for realistic simulation
        self.disease_patterns = {
            'Normal_Gait': {
                'emg_amplitude': 1.0,
                'emg_noise': 0.1,
                'asymmetry': 0.05,
                'movement_variability': 0.1,
                'balance_stability': 0.9
            },
            'Stroke_Hemiparetic': {
                'emg_amplitude': 0.6,
                'emg_noise': 0.2,
                'asymmetry': 0.7,  # High asymmetry
                'movement_variability': 0.3,
                'balance_stability': 0.4
            },
            'Cerebral_Palsy_Spastic': {
                'emg_amplitude': 1.8,  # High muscle activity
                'emg_noise': 0.3,
                'asymmetry': 0.4,
                'movement_variability': 0.5,
                'balance_stability': 0.3
            },
            'Parkinsonian_Gait': {
                'emg_amplitude': 0.3,  # Low muscle activity
                'emg_noise': 0.05,
                'asymmetry': 0.1,
                'movement_variability': 0.2,
                'balance_stability': 0.6
            },
            'Frailty_Gait': {
                'emg_amplitude': 0.4,
                'emg_noise': 0.15,
                'asymmetry': 0.2,
                'movement_variability': 0.4,
                'balance_stability': 0.5
            },
            'Vestibular_Dysfunction': {
                'emg_amplitude': 0.8,
                'emg_noise': 0.25,
                'asymmetry': 0.3,
                'movement_variability': 0.6,
                'balance_stability': 0.2  # Poor balance
            }
        }
        
        # Configure muscle names based on EMG sensor count
        if self.emg_sensors == 4:
            # 4 EMG sensors - each can handle 4 patches
            self.muscle_names = ['L_Thigh', 'R_Thigh', 'L_Calf', 'R_Calf']
            self.sensor_config = "4_EMG_4_IMU"
        else:
            # 8 EMG sensors - individual patches
            self.muscle_names = [
                'L_Thigh_1', 'L_Thigh_2', 'R_Thigh_1', 'R_Thigh_2',
                'L_Calf_1', 'L_Calf_2', 'R_Calf_1', 'R_Calf_2'
            ]
            self.sensor_config = "8_EMG_4_IMU"

        self.imu_locations = ['L_Thigh', 'R_Thigh', 'L_Calf', 'R_Calf']
        
        print(f"✅ Simulator initialized")
        print(f"📊 Diseases: {len(self.disease_patterns)}")
        print(f"💪 EMG sensors: {self.emg_sensors} ({len(self.muscle_names)} channels)")
        print(f"📱 IMU sensors: {len(self.imu_locations)}")
        print(f"🔧 Configuration: {self.sensor_config}")
    
    def generate_emg_data(self, muscle_name, pattern):
        """Generate realistic EMG data for a muscle."""
        
        # Base EMG signal (simulated muscle activation)
        base_frequency = 20 + random.uniform(-5, 5)  # 15-25 Hz base
        time_points = np.linspace(0, self.window_size/1000, self.window_size)
        
        # Generate base signal
        signal = np.sin(2 * np.pi * base_frequency * time_points)
        
        # Add higher frequency components (muscle fiber recruitment)
        for freq in [50, 100, 150]:
            amplitude = pattern['emg_amplitude'] * random.uniform(0.1, 0.3)
            signal += amplitude * np.sin(2 * np.pi * freq * time_points)
        
        # Apply disease-specific modifications
        signal *= pattern['emg_amplitude']
        
        # Add asymmetry for left/right differences
        if 'L_' in muscle_name and pattern['asymmetry'] > 0.3:
            signal *= (1 - pattern['asymmetry'])
        elif 'R_' in muscle_name and pattern['asymmetry'] > 0.3:
            signal *= (1 + pattern['asymmetry'] * 0.5)
        
        # Add noise
        noise = np.random.normal(0, pattern['emg_noise'], self.window_size)
        signal += noise
        
        # Convert to ADC values (0-4095 for ESP32)
        signal = np.abs(signal)  # Rectify
        signal = (signal * 1000 + 500).astype(int)  # Scale and offset
        signal = np.clip(signal, 0, 4095)
        
        return signal.tolist()
    
    def generate_imu_data(self, location, pattern):
        """Generate realistic IMU data for a sensor location."""
        
        # Simulate walking motion
        gait_frequency = 1.2 + random.uniform(-0.2, 0.2)  # ~1 Hz gait cycle
        time_points = np.linspace(0, self.window_size/1000, self.window_size)
        
        # Accelerometer data (m/s²)
        acc_data = {}
        
        # Vertical acceleration (gravity + movement)
        acc_data['z'] = 9.81 + 2 * np.sin(2 * np.pi * gait_frequency * time_points)
        
        # Forward/backward acceleration
        acc_data['x'] = 1.5 * np.sin(2 * np.pi * gait_frequency * time_points + np.pi/4)
        
        # Side-to-side acceleration
        acc_data['y'] = 0.8 * np.sin(2 * np.pi * gait_frequency * 2 * time_points)
        
        # Apply disease-specific modifications
        stability_factor = pattern['balance_stability']
        variability = pattern['movement_variability']
        
        for axis in acc_data:
            # Reduce stability
            acc_data[axis] *= stability_factor
            
            # Add variability/tremor
            tremor = variability * np.random.normal(0, 0.5, self.window_size)
            acc_data[axis] += tremor
            
            # Convert to sensor units (±16g range, 16-bit)
            acc_data[axis] = (acc_data[axis] * 2048).astype(int)
            acc_data[axis] = np.clip(acc_data[axis], -32768, 32767)
            acc_data[axis] = acc_data[axis].tolist()
        
        # Gyroscope data (°/s)
        gyro_data = {}
        
        # Rotational movements during gait
        gyro_data['x'] = 30 * np.sin(2 * np.pi * gait_frequency * time_points)  # Pitch
        gyro_data['y'] = 20 * np.sin(2 * np.pi * gait_frequency * time_points + np.pi/3)  # Roll
        gyro_data['z'] = 15 * np.sin(2 * np.pi * gait_frequency * 0.5 * time_points)  # Yaw
        
        for axis in gyro_data:
            # Apply stability and variability
            gyro_data[axis] *= stability_factor
            tremor = variability * np.random.normal(0, 10, self.window_size)
            gyro_data[axis] += tremor
            
            # Convert to sensor units (±2000°/s range, 16-bit)
            gyro_data[axis] = (gyro_data[axis] * 16.4).astype(int)
            gyro_data[axis] = np.clip(gyro_data[axis], -32768, 32767)
            gyro_data[axis] = gyro_data[axis].tolist()
        
        return {
            'accelerometer': acc_data,
            'gyroscope': gyro_data
        }
    
    def generate_data_packet(self):
        """Generate a complete data packet like ESP32 would send."""
        
        pattern = self.disease_patterns[self.current_disease]
        
        # Generate EMG data
        emg_data = []
        for i, muscle in enumerate(self.muscle_names):
            emg_samples = self.generate_emg_data(muscle, pattern)
            emg_data.append({
                'muscle': muscle,
                'channel': i,
                'samples': emg_samples
            })
        
        # Generate IMU data
        imu_data = []
        for i, location in enumerate(self.imu_locations):
            imu_samples = self.generate_imu_data(location, pattern)
            imu_data.append({
                'location': location,
                'sensor': i,
                'accelerometer': imu_samples['accelerometer'],
                'gyroscope': imu_samples['gyroscope']
            })
        
        # Create complete packet
        packet = {
            'timestamp': int(time.time() * 1000),
            'window_size': self.window_size,
            'sample_rate': self.sample_rate,
            'sensor_config': self.sensor_config,
            'emg_sensors': self.emg_sensors,
            'emg': emg_data,
            'imu': imu_data,
            'simulated_disease': self.current_disease  # For debugging
        }
        
        return packet
    
    def change_disease(self):
        """Randomly change the simulated disease."""
        diseases = list(self.disease_patterns.keys())
        self.current_disease = random.choice(diseases)
        print(f"🔄 Simulating: {self.current_disease}")
    
    async def connect_to_classifier(self):
        """Connect to the gait classifier and send data."""
        
        # Use appropriate port based on configuration
        if self.emg_sensors == 4:
            uri = "ws://localhost:8767"  # 4 EMG server port
        else:
            uri = "ws://localhost:8765"  # 8 EMG server port
        print(f"🔌 Connecting to classifier at {uri}")
        
        try:
            async with websockets.connect(uri) as websocket:
                print(f"✅ Connected to gait classifier!")
                self.is_running = True
                
                packet_count = 0
                
                while self.is_running:
                    # Generate and send data packet
                    packet = self.generate_data_packet()
                    await websocket.send(json.dumps(packet))
                    
                    packet_count += 1
                    
                    # Print status every 10 packets
                    if packet_count % 10 == 0:
                        print(f"📊 Sent {packet_count} packets | Current: {self.current_disease}")
                    
                    # Change disease occasionally
                    if packet_count % 25 == 0:
                        self.change_disease()
                    
                    # Wait for next window (50ms)
                    await asyncio.sleep(0.05)
                    
        except (websockets.exceptions.ConnectionClosed, ConnectionRefusedError):
            print("❌ Could not connect to classifier!")
            print("💡 Make sure the ESP32 real-time classifier is running:")
            print("   python esp32_realtime_classifier.py")
        except KeyboardInterrupt:
            print("\n🛑 Simulation stopped by user")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    def stop(self):
        """Stop the simulation."""
        self.is_running = False

def main():
    """Main function."""
    print("🚀 Starting ESP32 Data Simulator...")

    # Check for command line arguments first
    import sys
    emg_sensors = 4  # Default

    if len(sys.argv) > 1:
        # Command line argument provided
        if sys.argv[1] == "4":
            emg_sensors = 4
            print("📡 Using 4 EMG sensor configuration (from command line)")
        elif sys.argv[1] == "8":
            emg_sensors = 8
            print("📡 Using 8 EMG sensor configuration (from command line)")
        else:
            print(f"⚠️ Invalid argument '{sys.argv[1]}', using default 4 EMG")
            emg_sensors = 4
    elif sys.stdin.isatty():
        # Interactive mode - ask user only if no command line args
        print("Choose configuration:")
        print("1. 4 EMG sensors (recommended)")
        print("2. 8 EMG sensors")

        try:
            choice = input("Enter choice (1-2): ").strip()
            if choice == "2":
                emg_sensors = 8
                print("📡 Using 8 EMG sensor configuration")
            else:
                emg_sensors = 4
                print("📡 Using 4 EMG sensor configuration")
        except (EOFError, KeyboardInterrupt):
            emg_sensors = 4  # Default to 4 EMG
            print("📡 Using 4 EMG sensor configuration (default)")
    else:
        # Non-interactive mode - use default
        emg_sensors = 4
        print("📡 Auto-selecting 4 EMG configuration (default)")

    simulator = ESP32DataSimulator(emg_sensors=emg_sensors)

    try:
        # Run the simulator
        asyncio.run(simulator.connect_to_classifier())
    except KeyboardInterrupt:
        print("\n🛑 Simulator stopped")

    print("✅ Simulation complete!")

if __name__ == "__main__":
    main()
