<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 Real-Time Gait Disease Classifier</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #ffd700;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            animation: pulse 2s infinite;
        }
        
        .status-dot.connected {
            background: #44ff44;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .prediction-display {
            text-align: center;
            padding: 30px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            margin-bottom: 20px;
        }
        
        .current-prediction {
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
        
        .confidence-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4444, #ffaa00, #44ff44);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .sensor-card {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
        }
        
        .sensor-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #00ffff;
        }
        
        .history-panel {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }
        
        .history-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .log-panel {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin-bottom: 5px;
            opacity: 0.8;
        }
        
        .log-entry.error {
            color: #ff6b6b;
        }
        
        .log-entry.success {
            color: #51cf66;
        }
        
        .log-entry.info {
            color: #74c0fc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Real-Time Gait Disease Classifier</h1>
            <p>ESP32 + ML-Powered Clinical Gait Analysis</p>
        </div>
        
        <div class="status-panel">
            <div class="card">
                <h3>🔌 Connection Status</h3>
                <div class="connection-status">
                    <div class="status-dot" id="esp32-status"></div>
                    <span id="esp32-text">ESP32: Disconnected</span>
                </div>
                <div class="connection-status">
                    <div class="status-dot" id="classifier-status"></div>
                    <span id="classifier-text">Classifier: Offline</span>
                </div>
                <div class="connection-status">
                    <div class="status-dot" id="websocket-status"></div>
                    <span id="websocket-text">WebSocket: Disconnected</span>
                </div>
            </div>
            
            <div class="card">
                <h3>📊 System Stats</h3>
                <p>Samples Processed: <span id="sample-count">0</span></p>
                <p>Classifications: <span id="classification-count">0</span></p>
                <p>Accuracy: <span id="accuracy">--</span></p>
                <p>Uptime: <span id="uptime">00:00:00</span></p>
            </div>
            
            <div class="card">
                <h3>🎯 Model Info</h3>
                <p>Random Forest: ✅ Loaded</p>
                <p>SVM: ✅ Loaded</p>
                <p>Features: 112</p>
                <p>Diseases: 19</p>
            </div>
        </div>
        
        <div class="prediction-display">
            <div class="current-prediction" id="current-prediction">
                Waiting for data...
            </div>
            <div class="confidence-bar">
                <div class="confidence-fill" id="confidence-fill"></div>
            </div>
            <p>Confidence: <span id="confidence-text">--</span></p>
            <p>RF: <span id="rf-prediction">--</span> | SVM: <span id="svm-prediction">--</span></p>
        </div>
        
        <div class="data-grid">
            <div class="sensor-card">
                <h4>📈 EMG Activity</h4>
                <div class="sensor-value" id="emg-activity">-- mV</div>
            </div>
            <div class="sensor-card">
                <h4>🏃 Movement</h4>
                <div class="sensor-value" id="movement-level">-- units</div>
            </div>
            <div class="sensor-card">
                <h4>⚖️ Balance</h4>
                <div class="sensor-value" id="balance-score">-- %</div>
            </div>
            <div class="sensor-card">
                <h4>🔄 Symmetry</h4>
                <div class="sensor-value" id="symmetry-score">-- %</div>
            </div>
        </div>
        
        <div class="history-panel">
            <h3>📋 Recent Classifications</h3>
            <div id="classification-history">
                <div class="history-item">
                    <span>No classifications yet</span>
                    <span>--:--:--</span>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="connectToSystem()">🔌 Connect</button>
            <button class="btn btn-danger" onclick="disconnectFromSystem()">🔌 Disconnect</button>
            <button class="btn btn-primary" onclick="startSimulation()">🎮 Start Demo</button>
            <button class="btn btn-primary" onclick="clearHistory()">🗑️ Clear History</button>
        </div>
        
        <div class="log-panel">
            <div id="log-container">
                <div class="log-entry info">System initialized - Ready to connect</div>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;
        let startTime = Date.now();
        let sampleCount = 0;
        let classificationCount = 0;
        let classificationHistory = [];
        let demoMode = false;
        
        // Disease list for demo
        const diseases = [
            'Normal_Gait', 'Stroke_Hemiparetic', 'Cerebral_Palsy_Spastic',
            'Parkinsonian_Gait', 'Multiple_Sclerosis', 'Peripheral_Neuropathy',
            'Arthritis_Antalgic', 'Frailty_Gait', 'Vestibular_Dysfunction'
        ];
        
        function updateUptime() {
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('uptime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        function addLogEntry(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Keep only last 50 entries
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        function updateConnectionStatus(element, status, text) {
            const dot = document.getElementById(element + '-status');
            const textEl = document.getElementById(element + '-text');
            
            if (status) {
                dot.classList.add('connected');
                textEl.textContent = text + ': Connected';
            } else {
                dot.classList.remove('connected');
                textEl.textContent = text + ': Disconnected';
            }
        }
        
        function connectToSystem() {
            if (websocket) {
                websocket.close();
            }

            addLogEntry('Attempting to connect to classifier...', 'info');

            // Try to connect to the Python classifier
            websocket = new WebSocket('ws://localhost:8767');
            
            websocket.onopen = function(event) {
                isConnected = true;
                updateConnectionStatus('websocket', true, 'WebSocket');
                updateConnectionStatus('classifier', true, 'Classifier');
                addLogEntry('Connected to gait classifier!', 'success');

                // Identify as web interface
                websocket.send(JSON.stringify({
                    type: 'web_interface',
                    message: 'Web interface connected',
                    timestamp: Date.now()
                }));
            };
            
            websocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);

                    if (data.type === 'classification') {
                        handleClassificationResult(data);

                        // Update sensor data if available
                        if (data.sensor_data) {
                            document.getElementById('emg-activity').textContent = data.sensor_data.emg_activity.toFixed(1) + ' mV';
                            document.getElementById('movement-level').textContent = data.sensor_data.movement_level.toFixed(1) + ' units';
                            document.getElementById('balance-score').textContent = data.sensor_data.balance_score.toFixed(1) + ' %';
                            document.getElementById('symmetry-score').textContent = data.sensor_data.symmetry_score.toFixed(1) + ' %';
                        }
                    } else if (data.type === 'status') {
                        addLogEntry('Connected to classifier: ' + data.message, 'success');
                    } else if (data.type === 'welcome') {
                        addLogEntry(data.message, 'success');
                        if (data.esp32_connected) {
                            updateConnectionStatus('esp32', true, 'ESP32');
                            addLogEntry('ESP32 device is connected', 'success');
                        } else {
                            updateConnectionStatus('esp32', false, 'ESP32');
                            addLogEntry('Waiting for ESP32 connection...', 'warning');
                        }
                    }
                } catch (e) {
                    addLogEntry('Invalid data received: ' + e.message, 'error');
                }
            };
            
            websocket.onclose = function(event) {
                isConnected = false;
                updateConnectionStatus('websocket', false, 'WebSocket');
                updateConnectionStatus('classifier', false, 'Classifier');
                updateConnectionStatus('esp32', false, 'ESP32');
                addLogEntry('Connection closed', 'error');
            };
            
            websocket.onerror = function(error) {
                addLogEntry('Connection error - starting demo mode', 'error');
                startSimulation();
            };
        }
        
        function disconnectFromSystem() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            demoMode = false;
            isConnected = false;
            updateConnectionStatus('websocket', false, 'WebSocket');
            updateConnectionStatus('classifier', false, 'Classifier');
            updateConnectionStatus('esp32', false, 'ESP32');
            addLogEntry('Disconnected from system', 'info');
        }
        
        function startSimulation() {
            demoMode = true;
            updateConnectionStatus('esp32', true, 'ESP32');
            updateConnectionStatus('classifier', true, 'Classifier');
            updateConnectionStatus('websocket', true, 'WebSocket');
            addLogEntry('Demo mode started - simulating real-time data', 'success');
            
            // Simulate real-time classifications
            setInterval(() => {
                if (demoMode) {
                    simulateClassification();
                }
            }, 2000);
        }
        
        function simulateClassification() {
            const disease = diseases[Math.floor(Math.random() * diseases.length)];
            const confidence = 0.7 + Math.random() * 0.3;
            const rfConfidence = 0.6 + Math.random() * 0.4;
            const svmConfidence = 0.6 + Math.random() * 0.4;
            
            const result = {
                classification: disease,
                confidence: confidence > 0.9 ? 'High' : confidence > 0.7 ? 'Medium' : 'Low',
                rf_prediction: disease,
                svm_prediction: Math.random() > 0.8 ? diseases[Math.floor(Math.random() * diseases.length)] : disease,
                rf_confidence: rfConfidence,
                svm_confidence: svmConfidence,
                ensemble_confidence: confidence,
                timestamp: Date.now()
            };
            
            handleClassificationResult(result);
            
            // Simulate sensor data
            document.getElementById('emg-activity').textContent = (Math.random() * 500 + 100).toFixed(1) + ' mV';
            document.getElementById('movement-level').textContent = (Math.random() * 100 + 50).toFixed(1) + ' units';
            document.getElementById('balance-score').textContent = (Math.random() * 40 + 60).toFixed(1) + ' %';
            document.getElementById('symmetry-score').textContent = (Math.random() * 30 + 70).toFixed(1) + ' %';
        }
        
        function handleClassificationResult(data) {
            classificationCount++;
            sampleCount++;
            
            // Update main prediction display
            document.getElementById('current-prediction').textContent = data.classification.replace(/_/g, ' ');
            document.getElementById('confidence-text').textContent = 
                `${data.confidence} (${(data.ensemble_confidence * 100).toFixed(1)}%)`;
            document.getElementById('rf-prediction').textContent = data.rf_prediction.replace(/_/g, ' ');
            document.getElementById('svm-prediction').textContent = data.svm_prediction.replace(/_/g, ' ');
            
            // Update confidence bar
            const confidenceFill = document.getElementById('confidence-fill');
            confidenceFill.style.width = (data.ensemble_confidence * 100) + '%';
            
            // Update stats
            document.getElementById('sample-count').textContent = sampleCount;
            document.getElementById('classification-count').textContent = classificationCount;
            document.getElementById('accuracy').textContent = (data.ensemble_confidence * 100).toFixed(1) + '%';
            
            // Add to history
            addToHistory(data);
            
            addLogEntry(`Classified: ${data.classification} (${data.confidence})`, 'success');
        }
        
        function addToHistory(data) {
            const historyContainer = document.getElementById('classification-history');
            const time = new Date().toLocaleTimeString();
            
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <span>${data.classification.replace(/_/g, ' ')} (${data.confidence})</span>
                <span>${time}</span>
            `;
            
            historyContainer.insertBefore(historyItem, historyContainer.firstChild);
            
            // Keep only last 10 items
            while (historyContainer.children.length > 10) {
                historyContainer.removeChild(historyContainer.lastChild);
            }
        }
        
        function clearHistory() {
            document.getElementById('classification-history').innerHTML = 
                '<div class="history-item"><span>No classifications yet</span><span>--:--:--</span></div>';
            classificationHistory = [];
            addLogEntry('Classification history cleared', 'info');
        }
        
        // Update uptime every second
        setInterval(updateUptime, 1000);
        
        // Initialize and auto-connect
        addLogEntry('Web interface loaded - Auto-connecting to gait classifier', 'info');

        // Auto-connect after 1 second
        setTimeout(() => {
            connectToSystem();
        }, 1000);
    </script>
</body>
</html>
