# 🔬 ESP32 GAIT SENSOR HARDWARE SETUP - COMPLETE GUIDE

## 🎯 **Two Configuration Options**

### **🔥 Option 1: 4 EMG Sensors (RECOMMENDED)**
- **4x AD8232 EMG sensors** with 74HC4051 multiplexer
- **4x MPU6050 IMU sensors** with TCA9548A multiplexer
- **More cost-effective and easier to setup**
- **Each AD8232 can handle 4 EMG patches (16 total possible)**

### **⚡ Option 2: 8 EMG Sensors (Advanced)**
- **8x AD8232 EMG sensors** (individual patches)
- **4x MPU6050 IMU sensors** with TCA9548A multiplexer
- **Higher resolution but more complex**

---

## 🔥 **OPTION 1: 4 EMG SENSOR SETUP (RECOMMENDED)**

### **📦 Hardware Components:**
- **1x ESP32 DevKit** (WiFi enabled)
- **4x AD8232 EMG sensors**
- **4x MPU6050 IMU sensors**
- **1x TCA9548A I2C multiplexer** (for IMU sensors)
- **1x 74HC4051 analog multiplexer** (optional, for EMG expansion)
- **16x EMG electrodes/patches** (4 per AD8232)
- **Jumper wires and breadboards**

### **🔌 Pin Connections:**

#### **EMG Sensors (4x AD8232):**
```
AD8232 #1 (L_Thigh)  → ESP32 GPIO36 (ADC1_CH0)
AD8232 #2 (R_Thigh)  → ESP32 GPIO39 (ADC1_CH3)
AD8232 #3 (L_Calf)   → ESP32 GPIO34 (ADC1_CH6)
AD8232 #4 (R_Calf)   → ESP32 GPIO35 (ADC1_CH7)

Lead-off Detection:
LO+ pins: GPIO32, GPIO33, GPIO25, GPIO26
LO- pins: GPIO27, GPIO14, GPIO12, GPIO13
```

#### **74HC4051 Multiplexer (Optional):**
```
S0 → GPIO2
S1 → GPIO4
S2 → GPIO5
EN → GPIO15 (Enable, active LOW)
```

#### **TCA9548A I2C Multiplexer:**
```
SDA → GPIO21
SCL → GPIO22
VCC → 3.3V
GND → GND
Address: 0x70
```

#### **IMU Sensors (4x MPU6050 via TCA9548A):**
```
Channel 0: L_Thigh MPU6050
Channel 1: R_Thigh MPU6050
Channel 2: L_Calf MPU6050
Channel 3: R_Calf MPU6050

All connected to TCA9548A channels
```

### **📍 Sensor Placement:**
```
L_Thigh AD8232: 4 patches on left thigh muscles
  - Rectus Femoris
  - Vastus Lateralis
  - Vastus Medialis
  - Biceps Femoris

R_Thigh AD8232: 4 patches on right thigh muscles
  - Rectus Femoris
  - Vastus Lateralis
  - Vastus Medialis
  - Biceps Femoris

L_Calf AD8232: 4 patches on left calf muscles
  - Soleus
  - Medial Gastrocnemius
  - Lateral Gastrocnemius
  - Tibialis Anterior

R_Calf AD8232: 4 patches on right calf muscles
  - Soleus
  - Medial Gastrocnemius
  - Lateral Gastrocnemius
  - Tibialis Anterior
```

### **💻 Arduino Code:**
Use: `esp32_gait_sensor_4emg.ino`

### **🐍 Python Server:**
Use: `esp32_realtime_classifier_4emg.py`

---

## ⚡ **OPTION 2: 8 EMG SENSOR SETUP (ADVANCED)**

### **📦 Hardware Components:**
- **1x ESP32 DevKit** (WiFi enabled)
- **8x AD8232 EMG sensors** (individual)
- **4x MPU6050 IMU sensors**
- **1x TCA9548A I2C multiplexer** (for IMU sensors)
- **8x EMG electrodes/patches** (1 per AD8232)
- **Jumper wires and breadboards**

### **🔌 Pin Connections:**

#### **EMG Sensors (8x AD8232):**
```
AD8232 #1 (L_Thigh_1)  → ESP32 GPIO36 (ADC1_CH0)
AD8232 #2 (L_Thigh_2)  → ESP32 GPIO39 (ADC1_CH3)
AD8232 #3 (R_Thigh_1)  → ESP32 GPIO34 (ADC1_CH6)
AD8232 #4 (R_Thigh_2)  → ESP32 GPIO35 (ADC1_CH7)
AD8232 #5 (L_Calf_1)   → ESP32 GPIO32 (ADC1_CH4)
AD8232 #6 (L_Calf_2)   → ESP32 GPIO33 (ADC1_CH5)
AD8232 #7 (R_Calf_1)   → ESP32 GPIO25 (ADC2_CH8)
AD8232 #8 (R_Calf_2)   → ESP32 GPIO26 (ADC2_CH9)
```

#### **TCA9548A I2C Multiplexer:**
```
SDA → GPIO21
SCL → GPIO22
VCC → 3.3V
GND → GND
Address: 0x70
```

#### **IMU Sensors (4x MPU6050 via TCA9548A):**
```
Channel 0: L_Thigh MPU6050
Channel 1: R_Thigh MPU6050
Channel 2: L_Calf MPU6050
Channel 3: R_Calf MPU6050
```

### **💻 Arduino Code:**
Use: `esp32_gait_sensor.ino` (updated with TCA9548A support)

### **🐍 Python Server:**
Use: `esp32_realtime_classifier.py`

---

## 🔧 **Common Setup Steps**

### **1. Arduino IDE Setup:**
```bash
# Install ESP32 board package
# Install libraries:
- WiFi (built-in)
- WebSocketsServer
- Wire (built-in)
- MPU6050
- ArduinoJson
```

### **2. WiFi Configuration:**
```cpp
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";
```

### **3. Python Dependencies:**
```bash
pip install websockets asyncio numpy pandas scikit-learn
```

### **4. Testing:**
```bash
# Start Python server
python esp32_realtime_classifier_4emg.py  # For 4 EMG
# OR
python esp32_realtime_classifier.py       # For 8 EMG

# Upload Arduino code to ESP32
# Open Serial Monitor to see connection status
```

---

## 📊 **Comparison: 4 EMG vs 8 EMG**

| Feature | 4 EMG Setup | 8 EMG Setup |
|---------|-------------|-------------|
| **Cost** | Lower | Higher |
| **Complexity** | Simpler | More complex |
| **Patches** | 16 possible (4 per sensor) | 8 individual |
| **Resolution** | Good | Higher |
| **Setup Time** | Faster | Longer |
| **Recommended For** | Beginners, Prototyping | Advanced users, Research |

---

## 🎯 **Recommendation**

**Start with 4 EMG sensor setup** for:
- ✅ Easier implementation
- ✅ Lower cost
- ✅ Sufficient accuracy for most applications
- ✅ Expandable to 16 patches if needed

**Upgrade to 8 EMG sensors** for:
- 🔬 Research applications
- 📈 Maximum accuracy requirements
- 🏥 Clinical deployment

---

## 🚀 **Quick Start Commands**

### **For 4 EMG Setup:**
```bash
# 1. Upload esp32_gait_sensor_4emg.ino to ESP32
# 2. Start Python server
python esp32_realtime_classifier_4emg.py
# 3. Open web interface
# Open web_interface.html in browser
```

### **For 8 EMG Setup:**
```bash
# 1. Upload esp32_gait_sensor.ino to ESP32
# 2. Start Python server
python esp32_realtime_classifier.py
# 3. Open web interface
# Open web_interface.html in browser
```

**🎉 Your gait classification system is ready!**
