#!/usr/bin/env python3
"""
🚀 ESP32 REAL-TIME GAIT CLASSIFIER - 4 EMG VERSION (DEBUGGED)
Receives data from ESP32 with 4 EMG sensors via WiFi/WebSocket and classifies gait patterns
Optimized for 4 AD8232 EMG sensors + 4 MPU6050 IMU sensors with I2C multiplexers
"""

import asyncio
import websockets
import json
import numpy as np
import pandas as pd
import pickle
import time
import socket
from collections import deque
import warnings
import traceback
import logging
from datetime import datetime

warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

print("🚀 ESP32 REAL-TIME GAIT CLASSIFIER - 4 EMG VERSION (DEBUGGED)")
print("🔬 Optimized for 4 EMG + 4 IMU sensors with I2C multiplexers")
print("=" * 70)

class ESP32GaitClassifier4EMG:
    def __init__(self, model_path="production_gait_classifier.pkl"):
        """Initialize ESP32 real-time classifier for 4 EMG sensors."""
        
        self.model_loaded = False
        self.connections = {}  # Track connections
        self.data_received_count = 0
        self.last_classification = None
        
        # Load trained model
        print(f"📊 Loading model: {model_path}")
        try:
            with open(model_path, 'rb') as f:
                self.model_data = pickle.load(f)
            
            self.rf_model = self.model_data['rf_model']
            self.svm_model = self.model_data['svm_model']
            self.scaler = self.model_data['scaler']
            self.label_encoder = self.model_data['label_encoder']
            self.selected_features = self.model_data['selected_features']
            
            print(f"✅ Model loaded successfully")
            print(f"🔍 Features: {len(self.selected_features)}")
            print(f"🏥 Classes: {list(self.label_encoder.classes_)}")
            self.model_loaded = True
            
        except FileNotFoundError:
            print(f"❌ Model file not found: {model_path}")
            print("⚠️ Server will run without classification (data collection only)")
            self.model_loaded = False
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            print("⚠️ Server will run without classification (data collection only)")
            self.model_loaded = False
        
        # Data buffers
        self.classification_history = deque(maxlen=20)
        
        print(f"🎯 ESP32 4-EMG classifier {'ready!' if self.model_loaded else 'ready (no model)!'}")
    
    def extract_emg_features_from_esp32(self, emg_data):
        """Extract EMG features from ESP32 JSON data (4 sensors)."""
        features = {}
        
        try:
            # Map ESP32 4-sensor locations to dataset muscle names
            muscle_mapping = {
                'L_Thigh': 'L_Rectus Femoris',
                'R_Thigh': 'R_rectus femoris',
                'L_Calf': 'L_Soleus',
                'R_Calf': 'R_Soleus'
            }
            
            for emg_channel in emg_data:
                location = emg_channel.get('location', 'unknown')
                samples = np.array(emg_channel.get('samples', []))
                
                if len(samples) == 0:
                    print(f"⚠️ Empty EMG samples for location: {location}")
                    continue
                
                if location in muscle_mapping:
                    mapped_name = muscle_mapping[location]
                    
                    # Extract EMG features
                    # MAV (Mean Absolute Value)
                    mav = np.mean(np.abs(samples))
                    features[f"{mapped_name}_EMG 1 MAV"] = mav
                    
                    # WL (Waveform Length)
                    if len(samples) > 1:
                        wl = np.sum(np.abs(np.diff(samples)))
                        features[f"{mapped_name}_EMG 1 WL"] = wl
                    else:
                        features[f"{mapped_name}_EMG 1 WL"] = 0
                    
                    # ZC (Zero Crossings)
                    if len(samples) > 1:
                        zc = np.sum(np.diff(np.sign(samples)) != 0)
                        features[f"{mapped_name}_EMG 1 ZC"] = zc
                    else:
                        features[f"{mapped_name}_EMG 1 ZC"] = 0
                    
                    # SS (Slope Sign)
                    if len(samples) > 2:
                        diff_signal = np.diff(samples)
                        ss = np.sum(np.diff(np.sign(diff_signal)) != 0)
                        features[f"{mapped_name}_EMG 1 SS"] = ss
                    else:
                        features[f"{mapped_name}_EMG 1 SS"] = 0
                    
                    # Simulate additional muscle groups for better feature coverage
                    if location == 'L_Thigh':
                        features[f"L_Vastus Lateralis_EMG 1 MAV"] = mav * 0.8
                        features[f"L_Vastus Lateralis_EMG 1 WL"] = features[f"{mapped_name}_EMG 1 WL"] * 0.9
                        features[f"L_Vastus Lateralis_EMG 1 ZC"] = features[f"{mapped_name}_EMG 1 ZC"]
                        features[f"L_Vastus Lateralis_EMG 1 SS"] = features[f"{mapped_name}_EMG 1 SS"]
                    elif location == 'R_Thigh':
                        features[f"R_Vastus Lateralis_EMG 1 MAV"] = mav * 0.8
                        features[f"R_Vastus Lateralis_EMG 1 WL"] = features[f"{mapped_name}_EMG 1 WL"] * 0.9
                        features[f"R_Vastus Lateralis_EMG 1 ZC"] = features[f"{mapped_name}_EMG 1 ZC"]
                        features[f"R_Vastus Lateralis_EMG 1 SS"] = features[f"{mapped_name}_EMG 1 SS"]
                    elif location == 'L_Calf':
                        features[f"L_Medial Gastrocnemius_EMG 1 MAV"] = mav * 1.1
                        features[f"L_Medial Gastrocnemius_EMG 1 WL"] = features[f"{mapped_name}_EMG 1 WL"] * 1.0
                        features[f"L_Medial Gastrocnemius_EMG 1 ZC"] = features[f"{mapped_name}_EMG 1 ZC"]
                        features[f"L_Medial Gastrocnemius_EMG 1 SS"] = features[f"{mapped_name}_EMG 1 SS"]
                    elif location == 'R_Calf':
                        features[f"R_Medial Gastrocnemius_EMG 1 MAV"] = mav * 1.1
                        features[f"R_Medial Gastrocnemius_EMG 1 WL"] = features[f"{mapped_name}_EMG 1 WL"] * 1.0
                        features[f"R_Medial Gastrocnemius_EMG 1 ZC"] = features[f"{mapped_name}_EMG 1 ZC"]
                        features[f"R_Medial Gastrocnemius_EMG 1 SS"] = features[f"{mapped_name}_EMG 1 SS"]
                
                print(f"📊 EMG features extracted for {location}: MAV={mav:.2f}")
        
        except Exception as e:
            print(f"❌ Error extracting EMG features: {e}")
            traceback.print_exc()
        
        return features
    
    def extract_imu_features_from_esp32(self, imu_data):
        """Extract IMU features from ESP32 JSON data (4 sensors)."""
        features = {}
        
        try:
            # Map ESP32 IMU locations to dataset muscle names
            imu_mapping = {
                'L_Thigh': 'L_Rectus Femoris',
                'R_Thigh': 'R_rectus femoris', 
                'L_Calf': 'L_Soleus',
                'R_Calf': 'R_Soleus'
            }
            
            for imu_sensor in imu_data:
                location = imu_sensor.get('location', 'unknown')
                status = imu_sensor.get('status', 'unknown')
                
                if status == 'failed':
                    print(f"⚠️ IMU at {location} failed, skipping...")
                    continue
                
                if location in imu_mapping:
                    mapped_name = imu_mapping[location]
                    
                    # Process accelerometer data
                    acc_data = imu_sensor.get('accelerometer', {})
                    for axis in ['x', 'y', 'z']:
                        if axis in acc_data:
                            samples = np.array(acc_data[axis])
                            if len(samples) > 0:
                                axis_upper = axis.upper()
                                
                                features[f"{mapped_name}_ACC {axis_upper} mean"] = np.mean(samples)
                                features[f"{mapped_name}_ACC {axis_upper} std_dev"] = np.std(samples)
                                features[f"{mapped_name}_ACC {axis_upper} max"] = np.max(samples)
                    
                    # Process gyroscope data
                    gyro_data = imu_sensor.get('gyroscope', {})
                    for axis in ['x', 'y', 'z']:
                        if axis in gyro_data:
                            samples = np.array(gyro_data[axis])
                            if len(samples) > 0:
                                axis_upper = axis.upper()
                                
                                features[f"{mapped_name}_GYRO {axis_upper} mean"] = np.mean(samples)
                                features[f"{mapped_name}_GYRO {axis_upper} std_dev"] = np.std(samples)
                                features[f"{mapped_name}_GYRO {axis_upper} max"] = np.max(samples)
                
                print(f"📊 IMU features extracted for {location} (status: {status})")
        
        except Exception as e:
            print(f"❌ Error extracting IMU features: {e}")
            traceback.print_exc()
        
        return features
    
    def create_feature_vector(self, emg_features, imu_features):
        """Create feature vector matching the trained model."""
        
        try:
            # Combine all features
            all_features = {**emg_features, **imu_features}
            
            # Create feature vector with same order as training
            feature_vector = []
            missing_features = 0
            
            for feature_name in self.selected_features:
                if feature_name in all_features:
                    value = all_features[feature_name]
                    # Handle NaN/inf values
                    if np.isnan(value) or np.isinf(value):
                        feature_vector.append(0)
                        missing_features += 1
                    else:
                        feature_vector.append(value)
                else:
                    feature_vector.append(0)  # Fill missing features with 0
                    missing_features += 1
            
            if missing_features > 0:
                print(f"⚠️ Missing/invalid {missing_features}/{len(self.selected_features)} features (filled with zeros)")
            
            return np.array(feature_vector).reshape(1, -1)
        
        except Exception as e:
            print(f"❌ Error creating feature vector: {e}")
            return np.zeros((1, len(self.selected_features)))
    
    def classify_gait(self, feature_vector):
        """Classify gait pattern using trained models."""
        
        if not self.model_loaded:
            return {
                'prediction': 'Model not loaded',
                'rf_prediction': 'N/A',
                'svm_prediction': 'N/A',
                'confidence': 'N/A',
                'rf_confidence': 0,
                'svm_confidence': 0,
                'ensemble_confidence': 0
            }
        
        try:
            # Scale features
            scaled_features = self.scaler.transform(feature_vector)
            
            # Handle NaN values
            if np.isnan(scaled_features).any():
                scaled_features = np.nan_to_num(scaled_features, nan=0.0)
            
            # Get predictions from both models
            rf_pred = self.rf_model.predict(scaled_features)[0]
            svm_pred = self.svm_model.predict(scaled_features)[0]
            
            # Get prediction probabilities if available
            try:
                rf_proba = self.rf_model.predict_proba(scaled_features)[0]
                rf_confidence = np.max(rf_proba)
            except:
                rf_confidence = 0.5
            
            try:
                svm_proba = self.svm_model.predict_proba(scaled_features)[0]
                svm_confidence = np.max(svm_proba)
            except:
                svm_confidence = 0.5
            
            # Get class names
            rf_class = self.label_encoder.inverse_transform([rf_pred])[0]
            svm_class = self.label_encoder.inverse_transform([svm_pred])[0]
            
            # Ensemble decision with confidence weighting
            if rf_class == svm_class:
                final_prediction = rf_class
                confidence = "High"
                ensemble_confidence = (rf_confidence + svm_confidence) / 2
            else:
                # Use model with higher confidence
                if rf_confidence > svm_confidence:
                    final_prediction = rf_class
                    ensemble_confidence = rf_confidence
                else:
                    final_prediction = svm_class
                    ensemble_confidence = svm_confidence
                confidence = "Medium"
            
            return {
                'prediction': final_prediction,
                'rf_prediction': rf_class,
                'svm_prediction': svm_class,
                'confidence': confidence,
                'rf_confidence': rf_confidence,
                'svm_confidence': svm_confidence,
                'ensemble_confidence': ensemble_confidence
            }
        
        except Exception as e:
            print(f"❌ Error in classification: {e}")
            traceback.print_exc()
            return {
                'prediction': 'Classification error',
                'rf_prediction': 'Error',
                'svm_prediction': 'Error',
                'confidence': 'Low',
                'rf_confidence': 0,
                'svm_confidence': 0,
                'ensemble_confidence': 0
            }
    
    async def process_esp32_data(self, websocket, path=None):
        """Process incoming data from ESP32."""
        client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}" if websocket.remote_address else "unknown"
        print(f"🔌 New connection from {client_id}")
        
        # Track this connection
        self.connections[client_id] = {
            'websocket': websocket,
            'esp32_identified': False,
            'web_interface': False,
            'data_received': 0,
            'connected_time': datetime.now()
        }

        try:
            async for message in websocket:
                try:
                    # Check message size
                    message_size = len(message)
                    if message_size > 100000:  # 100KB limit
                        print(f"⚠️ Large message from {client_id}: {message_size} bytes")

                    # Parse JSON data
                    data = json.loads(message)
                    self.connections[client_id]['data_received'] += 1

                    # Check if it's sensor data from ESP32
                    if 'emg' in data and 'imu' in data:
                        if not self.connections[client_id]['esp32_identified']:
                            print(f"🎉 ESP32 IDENTIFIED! Connected from {client_id}")
                            self.connections[client_id]['esp32_identified'] = True
                        
                        self.data_received_count += 1
                        
                        # Log data details
                        sensor_config = data.get('sensor_config', 'unknown')
                        working_imus = data.get('working_imus', 'unknown')
                        window_size = data.get('window_size', 'unknown')
                        
                        print(f"📊 Data #{self.data_received_count}: {sensor_config}, IMUs: {working_imus}, Window: {window_size}")
                        
                        # Extract features
                        emg_features = self.extract_emg_features_from_esp32(data['emg'])
                        imu_features = self.extract_imu_features_from_esp32(data['imu'])
                        
                        print(f"🔍 Features extracted: EMG({len(emg_features)}), IMU({len(imu_features)})")
                        
                        # Create feature vector and classify only if model is loaded
                        if self.model_loaded:
                            feature_vector = self.create_feature_vector(emg_features, imu_features)
                            result = self.classify_gait(feature_vector)
                            
                            # Store in history
                            self.classification_history.append(result['prediction'])
                            self.last_classification = result
                            
                            # Display result
                            print(f"\n🎯 REAL-TIME CLASSIFICATION (4-EMG):")
                            print(f"   Timestamp: {data.get('timestamp', 'N/A')}")
                            print(f"   Prediction: {result['prediction']}")
                            print(f"   RF: {result['rf_prediction']} ({result['rf_confidence']:.3f})")
                            print(f"   SVM: {result['svm_prediction']} ({result['svm_confidence']:.3f})")
                            print(f"   Confidence: {result['confidence']} ({result['ensemble_confidence']:.3f})")
                            
                            # Show recent history
                            if len(self.classification_history) >= 5:
                                recent = list(self.classification_history)[-5:]
                                print(f"   Recent: {' → '.join(recent)}")
                        else:
                            result = {'prediction': 'No model', 'confidence': 'N/A'}
                            print(f"📊 Data received but no model loaded for classification")
                        
                        # Send response back to ESP32
                        response = {
                            'classification': result['prediction'],
                            'confidence': result.get('confidence', 'N/A'),
                            'sensor_config': '4_EMG_4_IMU',
                            'timestamp': time.time(),
                            'data_count': self.data_received_count,
                            'model_loaded': self.model_loaded
                        }
                        await websocket.send(json.dumps(response))

                        # Broadcast to web interface clients
                        await self.broadcast_to_web_interfaces({
                            'type': 'classification',
                            'prediction': result['prediction'],
                            'confidence': result.get('confidence', 'N/A'),
                            'rf_prediction': result.get('rf_prediction', 'N/A'),
                            'svm_prediction': result.get('svm_prediction', 'N/A'),
                            'rf_confidence': result.get('rf_confidence', 0),
                            'svm_confidence': result.get('svm_confidence', 0),
                            'ensemble_confidence': result.get('ensemble_confidence', 0),
                            'timestamp': time.time(),
                            'sensor_config': '4_EMG_4_IMU',
                            'esp32_connected': True
                        })
                    
                    elif 'message' in data:
                        if not self.connections[client_id]['esp32_identified'] and 'ESP32' in str(data['message']):
                            print(f"🎉 ESP32 IDENTIFIED! Connected from {client_id}")
                            self.connections[client_id]['esp32_identified'] = True
                        print(f"📨 ESP32 message: {data['message']}")

                        # Send acknowledgment
                        ack = {
                            'message': 'Hello from Python server!',
                            'model_loaded': self.model_loaded,
                            'server_time': datetime.now().isoformat()
                        }
                        await websocket.send(json.dumps(ack))

                    elif 'type' in data and data['type'] == 'web_interface':
                        # Web interface connection
                        print(f"🌐 WEB INTERFACE connected from {client_id}")
                        self.connections[client_id]['web_interface'] = True

                        # Send welcome message to web interface
                        welcome = {
                            'type': 'welcome',
                            'message': 'Connected to ESP32 Gait Classifier',
                            'model_loaded': self.model_loaded,
                            'server_time': datetime.now().isoformat(),
                            'esp32_connected': any(conn['esp32_identified'] for conn in self.connections.values())
                        }
                        await websocket.send(json.dumps(welcome))

                    else:
                        # Unknown data format
                        if not self.connections[client_id]['esp32_identified']:
                            print(f"⚠️ Unknown data format from {client_id}")
                            print(f"📝 Data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

                except json.JSONDecodeError as e:
                    print(f"❌ JSON decode error from {client_id}: {e}")
                except Exception as e:
                    print(f"❌ Error processing message from {client_id}: {e}")
                    traceback.print_exc()

        except websockets.exceptions.ConnectionClosed as e:
            if self.connections[client_id]['esp32_identified']:
                print(f"🔌 ESP32 disconnected from {client_id} - Data received: {self.connections[client_id]['data_received']}")
            else:
                print(f"🔌 Connection closed from {client_id} (was not ESP32)")
        except Exception as e:
            print(f"❌ Connection error from {client_id}: {e}")
            traceback.print_exc()
        finally:
            # Clean up connection tracking
            if client_id in self.connections:
                del self.connections[client_id]
    
    def print_status(self):
        """Print current server status."""
        print(f"\n📊 SERVER STATUS:")
        print(f"   Active connections: {len(self.connections)}")
        print(f"   Data packets received: {self.data_received_count}")
        print(f"   Model loaded: {self.model_loaded}")
        if self.last_classification:
            print(f"   Last prediction: {self.last_classification['prediction']}")
        print(f"   Classification history: {len(self.classification_history)} items")
        
        for client_id, info in self.connections.items():
            status = "ESP32" if info['esp32_identified'] else "Unknown"
            print(f"   {client_id}: {status} ({info['data_received']} packets)")

    async def broadcast_to_web_interfaces(self, message):
        """Broadcast message to all connected web interfaces."""
        web_interfaces = [
            conn['websocket'] for conn in self.connections.values()
            if conn.get('web_interface', False)
        ]

        if web_interfaces:
            message_json = json.dumps(message)
            for websocket in web_interfaces:
                try:
                    await websocket.send(message_json)
                except websockets.exceptions.ConnectionClosed:
                    # Connection closed, will be cleaned up later
                    pass
                except Exception as e:
                    print(f"❌ Error broadcasting to web interface: {e}")

    async def start_server(self):
        """Start WebSocket server to receive ESP32 data."""
        
        # Get local IP address
        try:
            # Connect to a remote server to get local IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
        except:
            # Fallback method
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)

        print(f"\n🌐 Starting WebSocket server on port 8767...")
        print(f"🖥️  Computer IP: {local_ip}")
        print(f"📡 ESP32 should connect to: ws://{local_ip}:8767")
        print(f"⚠️  Make sure ESP32 python_server_ip is set to: {local_ip}")
        print(f"🔧 Model status: {'Loaded ✅' if self.model_loaded else 'Not loaded ⚠️'}")

        # Start periodic status updates
        async def status_updater():
            while True:
                await asyncio.sleep(30)  # Print status every 30 seconds
                if len(self.connections) > 0 or self.data_received_count > 0:
                    self.print_status()

        # Start status updater as background task
        asyncio.create_task(status_updater())

        server = await websockets.serve(
            self.process_esp32_data,
            "0.0.0.0",  # Listen on all interfaces
            8767,       # Port for ESP32 connections
            max_size=2**20,  # 1MB max message size
            ping_interval=20,  # Ping every 20 seconds
            ping_timeout=10    # Timeout after 10 seconds
        )

        print(f"✅ Server started! Waiting for ESP32 connection...")
        print(f"🔍 Listening on all interfaces (0.0.0.0:8767)")
        print(f"📱 You can also connect from browser: ws://{local_ip}:8767")
        print("-" * 70)
        
        await server.wait_closed()

def main():
    """Main function to start the server."""
    print(f"🚀 Starting ESP32 Gait Classifier Server...")
    print(f"⏰ Server start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize classifier
    classifier = ESP32GaitClassifier4EMG(
        model_path="production_gait_classifier.pkl"
    )
    
    # Start WebSocket server
    try:
        print(f"\n🎯 Ready to receive ESP32 data!")
        asyncio.run(classifier.start_server())
    except KeyboardInterrupt:
        print(f"\n🛑 Server stopped by user")
        classifier.print_status()
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()