#!/usr/bin/env python3
"""
🚀 ESP32 REAL-TIME GAIT CLASSIFIER
Receives data from ESP32 via WiFi/WebSocket and classifies gait patterns
"""

import asyncio
import websockets
import json
import numpy as np
import pandas as pd
import pickle
import time
from collections import deque
import warnings
warnings.filterwarnings('ignore')

print("🚀 ESP32 REAL-TIME GAIT CLASSIFIER")
print("🔬 Connecting to ESP32 via WiFi...")
print("="*50)

class ESP32GaitClassifier:
    def __init__(self, model_path="production_gait_classifier.pkl", esp32_ip="*************", port=81):
        """Initialize ESP32 real-time classifier."""
        
        self.esp32_url = f"ws://{esp32_ip}:{port}"
        
        # Load trained model
        print(f"📊 Loading model: {model_path}")
        try:
            with open(model_path, 'rb') as f:
                self.model_data = pickle.load(f)
            
            self.rf_model = self.model_data['rf_model']
            self.svm_model = self.model_data['svm_model']
            self.scaler = self.model_data['scaler']
            self.label_encoder = self.model_data['label_encoder']
            self.selected_features = self.model_data['selected_features']
            
            print(f"✅ Model loaded successfully")
            print(f"🔍 Features: {len(self.selected_features)}")
            print(f"🏥 Classes: {len(self.label_encoder.classes_)}")
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return
        
        # Data buffers
        self.classification_history = deque(maxlen=20)
        
        print(f"🎯 ESP32 classifier ready!")
        print(f"🔌 Connecting to: {self.esp32_url}")
    
    def extract_emg_features_from_esp32(self, emg_data):
        """Extract EMG features from ESP32 JSON data."""
        features = {}
        
        # Map ESP32 muscle names to dataset muscle names
        muscle_mapping = {
            'L_Thigh_1': 'L_Rectus Femoris',
            'L_Thigh_2': 'L_Vastus Lateralis', 
            'R_Thigh_1': 'R_rectus femoris',
            'R_Thigh_2': 'R_Vastus Lateralis',
            'L_Calf_1': 'L_Soleus',
            'L_Calf_2': 'L_Medial Gastrocnemius',
            'R_Calf_1': 'R_Soleus',
            'R_Calf_2': 'R_Medial Gastrocnemius'
        }
        
        for emg_channel in emg_data:
            muscle_name = emg_channel['muscle']
            samples = np.array(emg_channel['samples'])
            
            if muscle_name in muscle_mapping:
                mapped_name = muscle_mapping[muscle_name]
                
                # Extract EMG features
                # MAV (Mean Absolute Value)
                mav = np.mean(np.abs(samples))
                features[f"{mapped_name}_EMG 1 MAV"] = mav
                
                # WL (Waveform Length)
                wl = np.sum(np.abs(np.diff(samples)))
                features[f"{mapped_name}_EMG 1 WL"] = wl
                
                # ZC (Zero Crossings)
                zc = np.sum(np.diff(np.sign(samples)) != 0)
                features[f"{mapped_name}_EMG 1 ZC"] = zc
                
                # SS (Slope Sign)
                diff_signal = np.diff(samples)
                ss = np.sum(np.diff(np.sign(diff_signal)) != 0)
                features[f"{mapped_name}_EMG 1 SS"] = ss
        
        return features
    
    def extract_imu_features_from_esp32(self, imu_data):
        """Extract IMU features from ESP32 JSON data."""
        features = {}
        
        # Map ESP32 IMU locations to dataset muscle names
        imu_mapping = {
            'L_Thigh': 'L_Rectus Femoris',
            'R_Thigh': 'R_rectus femoris', 
            'L_Calf': 'L_Soleus',
            'R_Calf': 'R_Soleus'
        }
        
        for imu_sensor in imu_data:
            location = imu_sensor['location']
            
            if location in imu_mapping:
                mapped_name = imu_mapping[location]
                
                # Process accelerometer data
                acc_data = imu_sensor['accelerometer']
                for axis in ['x', 'y', 'z']:
                    if axis in acc_data:
                        samples = np.array(acc_data[axis])
                        axis_upper = axis.upper()
                        
                        features[f"{mapped_name}_ACC {axis_upper} mean"] = np.mean(samples)
                        features[f"{mapped_name}_ACC {axis_upper} std_dev"] = np.std(samples)
                        features[f"{mapped_name}_ACC {axis_upper} max"] = np.max(samples)
                
                # Process gyroscope data
                gyro_data = imu_sensor['gyroscope']
                for axis in ['x', 'y', 'z']:
                    if axis in gyro_data:
                        samples = np.array(gyro_data[axis])
                        axis_upper = axis.upper()
                        
                        features[f"{mapped_name}_GYRO {axis_upper} mean"] = np.mean(samples)
                        features[f"{mapped_name}_GYRO {axis_upper} std_dev"] = np.std(samples)
                        features[f"{mapped_name}_GYRO {axis_upper} max"] = np.max(samples)
        
        return features
    
    def create_feature_vector(self, emg_features, imu_features):
        """Create feature vector matching the trained model."""
        
        # Combine all features
        all_features = {**emg_features, **imu_features}
        
        # Create feature vector with same order as training
        feature_vector = []
        missing_features = 0
        
        for feature_name in self.selected_features:
            if feature_name in all_features:
                feature_vector.append(all_features[feature_name])
            else:
                feature_vector.append(0)  # Fill missing features with 0
                missing_features += 1
        
        if missing_features > 0:
            print(f"⚠️ Missing {missing_features}/{len(self.selected_features)} features (filled with zeros)")
        
        return np.array(feature_vector).reshape(1, -1)
    
    def classify_gait(self, feature_vector):
        """Classify gait pattern using trained models."""
        
        # Scale features
        scaled_features = self.scaler.transform(feature_vector)
        
        # Handle NaN values
        if np.isnan(scaled_features).any():
            scaled_features = np.nan_to_num(scaled_features, nan=0.0)
        
        # Get predictions from both models
        rf_pred = self.rf_model.predict(scaled_features)[0]
        svm_pred = self.svm_model.predict(scaled_features)[0]
        
        # Get prediction probabilities if available
        try:
            rf_proba = self.rf_model.predict_proba(scaled_features)[0]
            rf_confidence = np.max(rf_proba)
        except:
            rf_confidence = 0.5
        
        try:
            svm_proba = self.svm_model.predict_proba(scaled_features)[0]
            svm_confidence = np.max(svm_proba)
        except:
            svm_confidence = 0.5
        
        # Get class names
        rf_class = self.label_encoder.inverse_transform([rf_pred])[0]
        svm_class = self.label_encoder.inverse_transform([svm_pred])[0]
        
        # Ensemble decision with confidence weighting
        if rf_class == svm_class:
            final_prediction = rf_class
            confidence = "High"
            ensemble_confidence = (rf_confidence + svm_confidence) / 2
        else:
            # Use model with higher confidence
            if rf_confidence > svm_confidence:
                final_prediction = rf_class
                ensemble_confidence = rf_confidence
            else:
                final_prediction = svm_class
                ensemble_confidence = svm_confidence
            confidence = "Medium"
        
        return {
            'prediction': final_prediction,
            'rf_prediction': rf_class,
            'svm_prediction': svm_class,
            'confidence': confidence,
            'rf_confidence': rf_confidence,
            'svm_confidence': svm_confidence,
            'ensemble_confidence': ensemble_confidence
        }
    
    async def process_esp32_data(self, websocket, path=None):
        """Process incoming data from ESP32."""
        client_ip = websocket.remote_address[0] if websocket.remote_address else "unknown"
        print(f"🔌 New connection from {client_ip}")

        esp32_identified = False

        try:
            async for message in websocket:
                try:
                    # Parse JSON data
                    data = json.loads(message)

                    # Check if it's sensor data from ESP32
                    if 'emg' in data and 'imu' in data:
                        if not esp32_identified:
                            print(f"🎉 ESP32 IDENTIFIED! Connected from {client_ip}")
                            esp32_identified = True
                        
                        # Extract features
                        emg_features = self.extract_emg_features_from_esp32(data['emg'])
                        imu_features = self.extract_imu_features_from_esp32(data['imu'])
                        
                        # Create feature vector
                        feature_vector = self.create_feature_vector(emg_features, imu_features)
                        
                        # Classify
                        result = self.classify_gait(feature_vector)
                        
                        # Store in history
                        self.classification_history.append(result['prediction'])
                        
                        # Display result
                        print(f"\n🎯 REAL-TIME CLASSIFICATION:")
                        print(f"   Timestamp: {data.get('timestamp', 'N/A')}")
                        print(f"   Prediction: {result['prediction']}")
                        print(f"   RF: {result['rf_prediction']} ({result['rf_confidence']:.3f})")
                        print(f"   SVM: {result['svm_prediction']} ({result['svm_confidence']:.3f})")
                        print(f"   Confidence: {result['confidence']} ({result['ensemble_confidence']:.3f})")
                        
                        # Show recent history
                        if len(self.classification_history) >= 5:
                            recent = list(self.classification_history)[-5:]
                            print(f"   Recent: {' → '.join(recent)}")
                        
                        # Send response back to ESP32
                        response = {
                            'classification': result['prediction'],
                            'confidence': result['confidence'],
                            'timestamp': time.time()
                        }
                        await websocket.send(json.dumps(response))
                    
                    elif 'message' in data:
                        if not esp32_identified and 'ESP32' in str(data['message']):
                            print(f"🎉 ESP32 IDENTIFIED! Connected from {client_ip}")
                            esp32_identified = True
                        print(f"📨 ESP32 message: {data['message']}")

                    else:
                        # Unknown data format - might be a test connection
                        if not esp32_identified:
                            print(f"⚠️ Non-ESP32 connection from {client_ip} (unknown data format)")

                except json.JSONDecodeError:
                    pass  # Log to web interface only
                except Exception as e:
                    pass  # Log to web interface only

        except websockets.exceptions.ConnectionClosed:
            if esp32_identified:
                print(f"🔌 ESP32 disconnected from {client_ip}")
            else:
                print(f"🔌 Connection closed from {client_ip} (was not ESP32)")
        except Exception as e:
            print(f"❌ Connection error from {client_ip}: {e}")
    
    async def start_server(self):
        """Start WebSocket server to receive ESP32 data."""
        print(f"🌐 Starting WebSocket server on port 8765...")
        print(f"📡 ESP32 should connect to: ws://YOUR_COMPUTER_IP:8765")
        
        server = await websockets.serve(
            self.process_esp32_data,
            "0.0.0.0",  # Listen on all interfaces
            8765
        )
        
        print(f"✅ Server started! Waiting for ESP32 connection...")
        await server.wait_closed()

def main():
    # Initialize classifier
    classifier = ESP32GaitClassifier(
        model_path="production_gait_classifier.pkl"
    )
    
    # Start WebSocket server
    try:
        asyncio.run(classifier.start_server())
    except KeyboardInterrupt:
        print(f"\n🛑 Stopping server...")

if __name__ == "__main__":
    main()
