clear; clc;
% Dataset Extractor Parameters (User-Determined)
sub_code = 2932; trial = 34; %num = "c"; 

% Load the original .mat file for raw data exported from Qualysis
%filename = "Gait LB - IOR " + num2str(trial) + "_" + num + ".mat";
filename = "Gait LB - IOR " + num2str(trial) + ".mat";
load(filename);

% Raw analog data extraction 
%data_struct = evalin('base', "Gait_LB___IOR_"+num2str(trial)+"_"+num); % Associated data structure
if trial > 30
    trial_idx = trial-30;
else
    trial_idx = trial;
end
data_struct = evalin('base', "Gait_LB___IOR_"+num2str(trial_idx)); % Associated data structure
T_analog = array2table([(0:1:(size(data_struct.Analog.Data.',1)-1)).'*(1/(1.259259226595261e+03)), ...
    data_struct.Analog.Data.'], 'VariableNames', ['Time' ,data_struct.Analog.Labels]); 
    %--> Table for raw analog data (IMU + EMG)
disp("The frequency of analog data (EMG + IMU) is: "+ round(data_struct.Analog.Frequency) + " Hz");

% Force plate data extraction
force_array = []; % Physical quantities (will contain: Force vector + Moment vector + Center of pressure vector)
force_label = "Time"; % Table headers (will contain: "Time" plus string headers of the previously mentioned quantities)
% Loop over each force plate (We have 4 force plates in the lab)
for i = 1 : 4
    force_array = [force_array, data_struct.Force(i).Force.', data_struct.Force(i).Moment.', data_struct.Force(i).COP.']; % Fill physical data
    force_label = [force_label, ["plate "+num2str(i)+" Fx", "plate "+num2str(i)+" Fy", "plate "+num2str(i)+" Fz", ...
        "plate "+num2str(i)+" Mx", "plate "+num2str(i)+" My", "plate "+num2str(i)+" Mz", ...
        "plate "+num2str(i)+" COPx", "plate "+num2str(i)+" COPy", "plate "+num2str(i)+" COPz"]]; % Fill headers
end
T_force = array2table([(0:1:(size(force_array,1)-1)).'*(1/(500)), force_array], 'VariableNames', force_label); 
    %--> Converting force array and headers to a table
disp("The frequency of force plate data/motion capture is: "+ round(data_struct.Force(1).Frequency) + " Hz");

% Marker location data extraction
marker_loc = []; marker_label = []; % Initial locations and headers (will contain: location of each marker + its header name)
% Loop over markers (we used 20 IOR lower body markers)
for i = 1 : 20
    % Each marker trajectory is defined by four quantities: x, y, z and residual
    for j = 1 : 4
        marker_loc = [marker_loc, reshape(data_struct.Trajectories.Labeled.Data(i,j,:), [], 1)];
        if j == 1
            l = " x";
        elseif j == 2
            l = " y";
        elseif j == 3
            l = " z";
        else
            l = " residual";
        end
        marker_label = [marker_label, data_struct.Trajectories.Labeled.Labels(i)+l]; % Append new header name
    end
end
T_marker = array2table(marker_loc, 'VariableNames', marker_label); % Convert arrays to a proper marker table
T_force_plus_marker = [T_force, T_marker]; % Combine force and marker tables into one table, since they have same frequency

% Force plate location data extraction 
% This part is almost static since plates don't move, more like metadata
location = []; label = [];
% Loop over plates
for i = 1 : 4
    for j = 1:4
        location = [location; data_struct.Force(i).ForcePlateLocation(j,:)]; % Plate location w.r.t lab reference frame
        label = [label, "plate "+num2str(i)+" v"+num2str(j)]; % Associated label
    end
end
T_plate_loc = array2table(location, 'VariableNames', ["x coordinate", "y coordinate", "z coordinate"]);
T_plate_loc.label = label.'; T_plate_loc = T_plate_loc(:, [end, 1:end-1]);

% Unit conversion for all physical quantities
% Analog quantities
varNames = T_analog.Properties.VariableNames;
% Accelerometers
accCols = contains(varNames, 'ACC');
T_analog{:, accCols} = T_analog{:, accCols} / 9.81; % accelerometer unit: g.
% Gyros
gyroCols = contains(varNames, 'GYRO');
T_analog{:, gyroCols} = T_analog{:, gyroCols} * (pi/180); % gyro unit: rad/sec.
% EMGs
emgCols = contains(varNames, 'EMG');
T_analog{:, emgCols} = T_analog{:, emgCols} * (1/1000.0); % EMG unit: millivolts

% Annotate trial based on Gyro data "Heuristics"
r_gyro = -T_analog{:, "R_Tibialis Anterior_GYRO X"}; % Right gyro (adjusted for heuristics)
l_gyro = -T_analog{:, "L_Tibialis Anterior_GYRO X"}; % Left gyro (adjusted for heuristics)
% Plot Gyro data from the gait bout for visual inspection
plot(1:size(l_gyro,1), l_gyro)
hold on
plot(1:size(r_gyro,1), r_gyro)
% Peaks
% TS hits
[TSpeaks, TSlocs] = findpeaks(-l_gyro, 'MinPeakDistance', 500, 'MinPeakHeight', 1.5); TSpeaks = -TSpeaks;
plot(TSlocs, TSpeaks, '*r')
% TSW hits
[TSWpeaks, TSWlocs] = findpeaks(-r_gyro, 'MinPeakDistance', 500, 'MinPeakHeight', 1.5); TSWpeaks = -TSWpeaks;
plot(TSWlocs, TSWpeaks, '*g')
% MST hits
[MSTpeaks, MSTlocs] = findpeaks(l_gyro, 'MinPeakDistance', 500, 'MinPeakHeight', 0.5); 
plot(MSTlocs, MSTpeaks, '*k')
% ISW hits
[ISWpeaks, ISWlocs] = findpeaks(r_gyro, 'MinPeakDistance', 500, 'MinPeakHeight', 0.5); 
plot(ISWlocs, ISWpeaks, '*b')

%%
% Assign correct peaks manually
LOCS.TS = [179,1675]; LOCS.TSW = [864,2384]; LOCS.MST = [1204,2732]; LOCS.ISW = [585,]; %--> User manual input
% Plot corrected transition peaks for visual validation after user update
plot(1:size(l_gyro,1), l_gyro)
hold on
plot(1:size(r_gyro,1), r_gyro)
plot(LOCS.TS, l_gyro(LOCS.TS), '*r')
plot(LOCS.TSW, r_gyro(LOCS.TSW), '*g')
plot(LOCS.MST, l_gyro(LOCS.MST), '*k')
plot(LOCS.ISW, r_gyro(LOCS.ISW), '*b')
%%
% Zero-crossings
LRlocs = find(r_gyro(1:end-1)<=0 & r_gyro(2:end)>0);
PSWlocs = find(l_gyro(1:end-1)<=0 & l_gyro(2:end)>0);
MSWlocs = find(r_gyro(1:end-1)>=0 & r_gyro(2:end)<0);
plot(1:size(l_gyro,1), l_gyro)
hold on
plot(1:size(r_gyro,1), r_gyro)
plot(LRlocs, r_gyro(LRlocs), '*r')
plot(PSWlocs, l_gyro(PSWlocs), '*g')
plot(MSWlocs, r_gyro(MSWlocs), '*k')
%%
% Assign correct zero-crossings manually
LOCS.LR = [590,2159,3726]; LOCS.PSW = [1406,2961]; LOCS.MSW = [102,1703,3276]; %--> User manual input
% Plot zero crossings for visual validation after user update
plot(1:size(l_gyro,1), l_gyro)
hold on
plot(1:size(r_gyro,1), r_gyro)
plot(LOCS.LR, r_gyro(LOCS.LR), '*r')
plot(LOCS.PSW, l_gyro(LOCS.PSW), '*g')
plot(LOCS.MSW, r_gyro(LOCS.MSW), '*k')
%%
% Write analog csv file
% Gait phase labels "automatic annotation"
labels = zeros(size(r_gyro)); sorted_LOCS = sort([1, LOCS.LR, LOCS.MST, LOCS.TS, LOCS.PSW, LOCS.ISW, LOCS.MSW, LOCS.TSW, size(r_gyro, 1)]);
for i = 1 : size(sorted_LOCS, 2)-1
    if any(LOCS.LR == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 7; % TSW
    elseif any(LOCS.MST == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 1; % LR
    elseif any(LOCS.TS == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 2; % MST
    elseif any(LOCS.PSW == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 3; % TS
    elseif any(LOCS.ISW == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 4; % PSW
    elseif any(LOCS.MSW == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 5; % ISW
    elseif any(LOCS.TSW == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 6; % MSW
    else
        if(labels(sorted_LOCS(i-1))+1 >7)
            labels(sorted_LOCS(i):sorted_LOCS(i+1)) = 1;
        else
            labels(sorted_LOCS(i):sorted_LOCS(i+1)) = labels(sorted_LOCS(i-1))+1;
        end
    end
end
T_analog.Mode = ones(size(r_gyro)); % Mode refers to gait activity (ground-level walking in this case : 1)
T_analog.phase = labels; % Phase labels
% Define the raw analog filename and write the table to a CSV file
%filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + num + "_Analog_raw" + ".csv";
filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + "_Analog_raw" + ".csv";
writetable(T_analog, filename);

% Write force/marker CSV file
% Annotate the gait phases for the "force plus markers" table
% This is done by squeezing the original gyro transitions into the force/marker refresh rate band
LOCS.LR = round(LOCS.LR*(data_struct.Force(1).Frequency/data_struct.Analog.Frequency));
LOCS.MST = round(LOCS.MST*(data_struct.Force(1).Frequency/data_struct.Analog.Frequency));
LOCS.TS = round(LOCS.TS*(data_struct.Force(1).Frequency/data_struct.Analog.Frequency));
LOCS.PSW = round(LOCS.PSW*(data_struct.Force(1).Frequency/data_struct.Analog.Frequency));
LOCS.ISW = round(LOCS.ISW*(data_struct.Force(1).Frequency/data_struct.Analog.Frequency));
LOCS.MSW = round(LOCS.MSW*(data_struct.Force(1).Frequency/data_struct.Analog.Frequency));
LOCS.TSW = round(LOCS.TSW*(data_struct.Force(1).Frequency/data_struct.Analog.Frequency));
labels = zeros(height(T_force_plus_marker),1); 
sorted_LOCS = sort([1, LOCS.LR, LOCS.MST, LOCS.TS, LOCS.PSW, LOCS.ISW, LOCS.MSW, LOCS.TSW, height(T_force_plus_marker)]);
for i = 1 : size(sorted_LOCS, 2)-1
    if any(LOCS.LR == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 7;
    elseif any(LOCS.MST == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 1;
    elseif any(LOCS.TS == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 2;
    elseif any(LOCS.PSW == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 3;
    elseif any(LOCS.ISW == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 4;
    elseif any(LOCS.MSW == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 5;
    elseif any(LOCS.TSW == sorted_LOCS(i+1))
        labels(sorted_LOCS(i):sorted_LOCS(i+1)-1) = 6;
    else
        if(labels(sorted_LOCS(i-1))+1 >7)
            labels(sorted_LOCS(i):sorted_LOCS(i+1)) = 1;
        else
            labels(sorted_LOCS(i):sorted_LOCS(i+1)) = labels(sorted_LOCS(i-1))+1;
        end
    end
end
T_force_plus_marker.Mode = ones(height(T_force_plus_marker),1);
T_force_plus_marker.phase = labels;
% Define the force/marker filename and write the table to a CSV file
%filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + num + "_Force_Motion_raw" + ".csv";
filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + "_Force_Motion_raw" + ".csv";
writetable(T_force_plus_marker, filename);

% Define the plate location filename and write the table to a CSV file
%filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + num + "_Plate_Location" + ".csv";
filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + "_Plate_Location" + ".csv";
writetable(T_plate_loc, filename);
%%
% Processed data extraction
% Body physical quantities (acc, velocity, momemnt, power, etc.)
%jsonText = fileread("IOR "+num2str(trial)+"_"+num+".json");
jsonText = fileread("IOR "+num2str(trial)+".json");
% Decode the JSON string into a MATLAB structure
data = jsondecode(jsonText);
% Examine Visual3D ouput quantities one by one
T_body_processed = table; % Initialize empty table
T_body_processed.Time = T_force_plus_marker.Time; % Assign force/marker time stamps to processed quantities (same refresh rate)
for i = 1 : size(data.Visual3D,1)
    % Search for the physical quantities of interest in the LINK_MODEL_BASED property
    if data.Visual3D(i).type == "LINK_MODEL_BASED" && (contains(string(data.Visual3D(i).name), "Ang_Acc") || ...
            contains(string(data.Visual3D(i).name), "Ang_Vel") || (contains(string(data.Visual3D(i).name), "Angles")&& ...
            ~contains(string(data.Visual3D(i).name), "Angles_")) || ...
            contains(string(data.Visual3D(i).name), "Moment") || contains(string(data.Visual3D(i).name), "Power") || ...
            contains(string(data.Visual3D(i).name), "COP_rt_") || contains(string(data.Visual3D(i).name), "CG_rt_LFT") || ...
            contains(string(data.Visual3D(i).name), "CG_rt_RFT"))
        T_body_processed.(string(data.Visual3D(i).name) + " " + string(data.Visual3D(i).signal(1).component)) = ...
            data.Visual3D(i).signal(1).data; % Signal + vector component
        T_body_processed.(string(data.Visual3D(i).name) + " " + string(data.Visual3D(i).signal(2).component)) = ...
            data.Visual3D(i).signal(2).data;
        T_body_processed.(string(data.Visual3D(i).name) + " " + string(data.Visual3D(i).signal(3).component)) = ...
            data.Visual3D(i).signal(3).data;
    end
end
% Annotate the processed body values (same frequency as force, so we'll use force labels)
T_body_processed.Mode = ones(height(T_body_processed),1);
T_body_processed.phase = labels;

% Add gait event labels based on the software's inspection (Heel strike, toe off, etc.) 
for i = 1 : size(data.Visual3D,1)
    if data.Visual3D(i).type == "EVENT_LABEL" && string(data.Visual3D(i).name) ~= "start" && ...
            string(data.Visual3D(i).name) ~= "end" && string(data.Visual3D(i).name) ~= "RANGESTART" && ...
            string(data.Visual3D(i).name) ~= "temp1" && string(data.Visual3D(i).name) ~= "temp2"
        column_padded = [data.Visual3D(i).signal.data; NaN(height(T_body_processed) - length(data.Visual3D(i).signal.data), 1)];
        T_body_processed.(string(data.Visual3D(i).name) + " " + string(data.Visual3D(i).signal.component)) = ...
            column_padded;
    end
end
% Define the filename for proccessed body motion values and write to CSV file
%filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + num + "_Body_Motion_processed" + ".csv";
filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + "_Body_Motion_processed" + ".csv";
writetable(T_body_processed, filename);

% Processed IMU and EMG data computation
T_analog_processed = table;
T_analog_processed.Time = T_analog.Time; % Processed analog has the same time stamps as raw analog
analogHeaders = T_analog.Properties.VariableNames; % Analog signal headers
% IMU signals processing
[b_low, a_low] = butter(6, 20/(round(data_struct.Analog.Frequency)/2), 'low'); % 6th order low-pass butterworth filter (cutoff: 20 Hz)
for i = 1:size(analogHeaders, 2)
    head = analogHeaders(i); head = head{1};
    if contains(head,"ACC") || contains(head,"GYRO") 
        T_analog_processed.(head) = filtfilt(b_low, a_low, T_analog.(head)); % Filter and add to processed table
    end
end
% EMG
[b_high, a_high] = butter(6, 20/(round(data_struct.Analog.Frequency)/2), 'high'); % 6th order high-pass butterworth filter (20 Hz cutoff)
[b_low, a_low] = butter(6, 350/(round(data_struct.Analog.Frequency)/2), 'low'); % 6th order low-pass butterworth filter (350 Hz cutoff)
[b_notch_60, a_notch_60] = butter(6, [60-3,60+3]/(round(data_struct.Analog.Frequency)/2), 'stop'); % 6th order notch butterworth filter (60 Hz)
[b_notch_180, a_notch_180] = butter(6, [180-3,180+3]/(round(data_struct.Analog.Frequency)/2), 'stop'); % 6th order notch butterworth filter (180 Hz)
[b_notch_300, a_notch_300] = butter(6, [300-3,300+3]/(round(data_struct.Analog.Frequency)/2), 'stop'); % 6th order notch butterworth filter (300 Hz)
for i = 1:size(analogHeaders, 2)
    head = analogHeaders(i); head = head{1};
    % Search for EMG signals of interest
    if contains(head,"EMG")
        T_analog_processed.(head) = filtfilt(b_high, a_high, T_analog.(head)); % HPF
        T_analog_processed.(head) = filtfilt(b_low, a_low, T_analog_processed.(head)); % LPF
        T_analog_processed.(head) = filtfilt(b_notch_60, a_notch_60, T_analog_processed.(head)); % NPF @60 Hz
        T_analog_processed.(head) = filtfilt(b_notch_180, a_notch_180, T_analog_processed.(head)); % NPF @180 Hz 
        T_analog_processed.(head) = filtfilt(b_notch_300, a_notch_300, T_analog_processed.(head)); % NPF @300 Hz
    end
end
T_analog_processed.Mode = T_analog.Mode; T_analog_processed.phase = T_analog.phase;
% Define the filename and write the processed analog table to a CSV file
%filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + num + "_Analog_processed" + ".csv";
filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + "_Analog_processed" + ".csv";
writetable(T_analog_processed, filename);
%%
% Feature csv files
ms_delay = 50; % Delay in ms
win_sz = ceil(ms_delay*(10^-3)*data_struct.Analog.Frequency); % 50 ms window size
delay_sz = round(win_sz/3); % delay size is 1/3 of window size
for delay = 0 : delay_sz : win_sz
    T_feature = table; % Feature table associated with the current delay
    % Compute IMU & EMG features
    for j = 1:size(analogHeaders, 2)
        head = analogHeaders(j); head = head{1};
        if contains(head,"ACC") || contains(head,"GYRO") 
            T_feature.(string(head) + " mean") = feat_extract(T_analog.(head)(delay+1:end), "mean", win_sz); 
            T_feature.(string(head) + " median") = feat_extract(T_analog.(head)(delay+1:end), "median", win_sz);
            T_feature.(string(head) + " std_dev") = feat_extract(T_analog.(head)(delay+1:end), "std_dev", win_sz); 
            T_feature.(string(head) + " initial") = feat_extract(T_analog.(head)(delay+1:end), "initial", win_sz);
            T_feature.(string(head) + " final") = feat_extract(T_analog.(head)(delay+1:end), "final", win_sz); 
            T_feature.(string(head) + " max") = feat_extract(T_analog.(head)(delay+1:end), "max", win_sz);
            T_feature.(string(head) + " min") = feat_extract(T_analog.(head)(delay+1:end), "min", win_sz); 
        elseif contains(head,"EMG")
            T_feature.(string(head) + " MAV") = feat_extract(T_analog_processed.(head)(delay+1:end), "MAV", win_sz); 
            T_feature.(string(head) + " WL") = feat_extract(T_analog_processed.(head)(delay+1:end), "WL", win_sz); 
            T_feature.(string(head) + " ZC") = feat_extract(T_analog_processed.(head)(delay+1:end), "ZC", win_sz); 
            T_feature.(string(head) + " SS") = feat_extract(T_analog_processed.(head)(delay+1:end), "SS", win_sz);
            AR_coeff = AR_extract(T_analog_processed.(head)(delay+1:end), win_sz); 
            for k = 1 : 6
                T_feature.(string(head) + " AR coeff" + num2str(k)) = AR_coeff(:,k);
            end
        end
    end
    T_feature.Mode = ones(height(T_feature),1); T_feature.phase = feat_extract(T_analog.("phase")(delay+1:end), "phase", win_sz);
    % Define the filename and write the processed analog table to a CSV file
    filename = "AB" + num2str(sub_code) + "_Trial_" + num2str(trial,'%03d') + "_feat_"+ num2str(round((delay/data_struct.Analog.Frequency)*1000)) + "ms.csv";
    writetable(T_feature, filename);
end

function feat_vector = feat_extract(col, feat, w)
% Extracts features from column vector by diving it into windows and computing the feature for each
feat_vector = zeros(floor(length(col)/w),1);
for i = 1:w:floor(length(col)/w)*w
    if feat == "mean"
        feat_vector(ceil(i/w)) = mean(col(i:i+w-1));
    elseif feat == "median"
        feat_vector(ceil(i/w)) = median(col(i:i+w-1));
    elseif feat == "std_dev"
        feat_vector(ceil(i/w)) = std(col(i:i+w-1));
    elseif feat == "initial"
        feat_vector(ceil(i/w)) = col(i);
    elseif feat == "max"
        feat_vector(ceil(i/w)) = max(col(i:i+w-1));
    elseif feat == "min"
        feat_vector(ceil(i/w)) = min(col(i:i+w-1));
    elseif feat == "MAV"
        feat_vector(ceil(i/w)) = mean(abs(col(i:i+w-1)));
    elseif feat == "WL"
        feat_vector(ceil(i/w)) = sum(abs(diff(col(i:i+w-1))));
    elseif feat == "ZC"
        feat_vector(ceil(i/w)) = nnz(diff(col(i:i+w-1) > 0));
    elseif feat == "SS"
        feat_vector(ceil(i/w)) = nnz(diff(sign(diff(col(i:i+w-1)))) ~= 0);
    elseif feat == "phase"
        feat_vector(ceil(i/w)) = col(i);
    end
end
end

function AR_matrix = AR_extract(col, w)
% Extracts autoregressive coefficients from column vector by diving it into windows and computing the coefficients for each
AR_matrix = zeros(floor(length(col)/w),6);
for i = 1:w:floor(length(col)/w)*w
    [coeff, ~] = aryule(col(i:i+w-1), 6);
    for j = 1 : 6
        AR_matrix(ceil(i/w), j) = coeff(j);
    end
end
end